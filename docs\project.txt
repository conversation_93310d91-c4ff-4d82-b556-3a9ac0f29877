1. 项目概述
YouTube下载站支持单次/批量下载。单次下载指输入一个视频/Shorts链接，下载其视音频流/字幕/评论/缩略图/描述，或剪辑视音频、制作GIF/铃声等。批量下载指输入一个播放列表/频道链接，或多个视频链接，对这些视频及可选内容（视音频/字幕等）进行配置、处理和批量下载。
2. 核心业务概念：任务
用户发起的每一个下载/处理请求都被定义为一个“任务”。任务是系统追踪进度、管理状态和提供反馈的基本单元，分为以下类型：
•	执行任务: 实际执行下载、转换、解析等具体操作的最小工作单元，由工作节点从消息队列中消费并处理。根据其发起源头又分为：
o	单次任务： 在“视频下载页”针对单个YouTube视频/Shorts发起的特定操作（如下载1080p视频、制作GIF、下载评论）。每个操作独立生成一个“单次任务”，与发起用户关联，不属于任何批量集合。
o	批量子任务： “批量任务”启动后，系统为其每个选定视频的每项具体处理内容（如“下载视频A的MP4文件”、“下载视频B的评论”）创建的独立“批量子任务”。与“单次任务”类似，但关联到其对应的“批量任务ID”。它们的总和构成父任务的整体进度。
•	批量任务（父任务）： 通过输入播放列表/频道/多个视频链接启动的对多个视频进行批量处理的操作，是管理其所有子任务的逻辑容器。
某些执行任务（如“剪辑下载视频”）内部包含多个顺序步骤（例如：下载原始流->剪辑片段->转换格式）。这些步骤是其内部执行流程，不再拆分为独立的可追踪任务，总体进度基于内部步骤的完成情况来更新。每个执行任务包含以下核心属性：
•	任务ID： 全局唯一标识符。
•	用户ID： 关联任务发起者。
•	批量任务ID (可选)：若为批量子任务，则关联父任务ID。
•	任务名称： 任务的具体内容简述。
•	任务类型： 明确操作类型（如 VIDEO_DOWNLOAD, AUDIO_CONVERT, GIF_CREATE等）。
•	参数： 执行任务所需的具体参数（如 videoId, 分辨率, 时间戳等）。
•	状态：
o	待处理 (Pending)： 已成功创建并记录在系统，等待核心服务调度。
o	排队中 (Queued)： 已发送至消息队列，等待工作节点拉取执行。
o	处理中 (Processing)：正在被工作节点执行（可能包含内部步骤如解析中、下载中、转换中）。
o	完成 (Completed)： 成功执行并生成结果。
o	失败 (Failed)： 执行失败。
o	已取消 (Cancelled)： 被用户/系统终止。
•	进度： 完成百分比。
•	创建/开始/结束时间。
•	结果链接/路径： 生成文件的下载链接或存储路径。
•	错误信息： 失败时的错误描述。
3. 功能需求
3.1. 首页
•	通用链接输入：单行输入框（内容非空时显示清空“x”）接收单个YouTube视频/Shorts/播放列表/频道链接。实时前端验证格式（提取核心ID，忽略URL参数；若v=和list=共存，优先识别v=）。无效时红框/提示。有效时绿框/“搜索”按钮可用，点击后禁用控件，显示加载。
•	多视频输入：可展开区域。多行文本框，每行一个视频/Shorts链接（2-100条）。支持手动粘贴、拖拽TXT文件（UTF-8编码，内容覆盖现有）。提供“清除”按钮。另有“上传TXT”按钮功能同拖拽。前端实时验证，忽略非视频/Shorts链接（如播放列表/频道，并提示），截取前100条（若超限）。显示有效链接数。有效数量在[2, 100]范围内时“搜索”按钮可用。
•	链接处理：单个视频/Shorts提取videoId，跳转/download?videoId={videoId}。单个播放列表/频道（来自通用链接输入）或多个视频链接（来自多视频输入）被发送到后端创建批量任务（父任务）并返回任务标识，跳转/batch-download?taskId={taskId}。
•	全局状态：加载时显示顶部进度条，失败显示红色非技术性错误提示。
3.2. 视频下载页(/download?videoId={videoId}) 
•	数据获取：页面加载时向后端同步API请求获取指定videoId详情。核心服务调用工作节点解析视频元数据、可用流、字幕等信息，并智能整理后同步返回前端。前端显示骨架屏，成功则渲染页面，失败显示错误信息。核心服务缓存数据30分钟。
•	内容概览(左侧)
o	视频信息：最佳缩略图（点击弹窗播放）、标题、频道、上传日期、时长、观看/点赞/评论数、描述（默认三行，可展开）。
o	下载按钮：“下载缩略图”（JPG/PNG），“下载描述”（TXT），“下载评论”（XLSX/CSV，免费限100条，付费可选100/500/1000条，排序热门/最新，评论禁用则按钮禁用）。
•	下载选项卡(右侧)：
o	“视频”选项卡：
	视频流列表：显示基于视频可用质量等级（分辨率、帧率）从高到低排序的完整（含音频）视频文件下载选项。后端智能整理格式，优先带音频的MP4流，其次合并DASH MP4与最佳M4A封装为MP4，或合并WebM视频流与最佳M4A封装为MKV（不转码）。前端仅展示这些已整合并包含音频的选项。每项显示质量、分辨率、FPS、预估文件大小、格式。“下载”按钮。免费用户最高1080p 30fps，更高质量选项灰显并提示升级。
	视频剪辑：“启用剪辑”开关，开启后紧靠开关右侧显示开始/结束时间输入框（HH:MM:SS）(开始≥0，结束>开始，结束≤总时长，片段≥1秒)。有效时，视频流选项“下载”按钮旁新增“剪辑下载”按钮，按钮文本动态显示对应剪辑后视频的预估文件大小。(例如：“剪辑下载 (12.5MB)”)。
	动图 (GIF) 制作：独立面板。输入开始/结束时间（1-30秒），帧率（5-20fps，默认10），宽度（100-800px，默认400，高宽比固定）。“生成GIF”按钮。完成后显示预览/下载。
o	“音频”选项卡：
	音频流列表：显示两种最佳原始音频格式（M4A和WebM，M4A居首）及据最佳文件转码的MP3格式（按比特率排序，320/256/192/128/64kbps）。显示预估大小。“下载”按钮。免费用户最高192k MP3，更佳选项灰显并提示升级。
	音频剪辑：参考视频剪辑功能。
	铃声制作：独立面板。输入开始/结束时间（1-30秒）。含淡入/淡出（默认勾选2秒）。输出格式：M4R（默认），MP3。质量固定128kbps。“制作铃声”按钮。完成后显示播放器/下载。
o	“字幕”选项卡：
	原始字幕列表：视频作者上传和ASR字幕。按语言A-Z排序。每项提供“查看”（弹窗预览，含时间码和文本，可复制全部）和“下载”（SRT/VTT/TXT）。旁有自动翻译语言下拉框（YouTube提供语言），选择后“查看”/“下载”对应翻译版本。
	多语言字幕制作：独立面板。选择一个原始字幕作基础语言，再选择最多2个目标翻译语言。提供“查看”按钮（弹窗预览格式：每行原文后跟翻译）。“下载”按钮（SRT/VTT/TXT）。
•	任务处理与反馈：点击下载/制作按钮，前端发送请求至后端创建单次任务。右侧显示非模态可折叠任务列表，显示当前页面发起的所有与此视频相关的任务。
o	任务项：任务名称，状态、进度条/百分比。“取消”按钮。
o	完成任务：状态“完成”，自动触发浏览器下载，提供备用“文件链接”。
o	失败任务：状态“失败”，显示错误信息，“重试”按钮。
o	用户离开提示：弹窗提示关闭页面可能中断任务。
o	状态恢复：重新打开页面，前端请求后端恢复用户任务列表，WebSocket实时更新进度。
3.3. 批量下载页(/batch-download?taskId={taskId})
•	数据获取：页面加载时向后端同步API请求获取指定taskId的批量任务（父任务）详情。核心服务查询父任务记录获取来源信息。若来源是播放列表或频道，核心服务调用工作节点分批解析，首次获取总体信息及第一批视频元数据，前端渲染后用户滚动触发下一批解析。对于多视频链接来源，页面加载时不预解析。列表中仅显示用户提交的视频ID占位符，视频详细信息将在后续批量子任务执行时动态获取。首次数据获取期间前端显示骨架屏。成功则渲染任务源信息和视频列表。失败显示错误信息。页面刷新或重访时恢复任务最新状态。
•	任务源信息：页面顶部显示来源（播放列表/频道/多视频），元数据、总视频数。提示“最多处理前1000个”。
•	视频列表：对于播放列表/频道来源，分批渲染视频数据。免费用户最多加载前100个视频的元数据，付费用户最多1000个，达到限制时提示升级。每个视频项显示索引号/缩略图/视频标题/频道名称/上传日期/观看数，另提供复选框，可手动勾选/取消。列表提供全选/全不选，显示“已选中[x]/[y]个视频”。对于多视频来源，初始列表仅显示视频ID占位符。
•	批量下载配置： 任务源信息加载完毕后显示。
o	内容类型：复选框（视频、音频、字幕、评论、缩略图、描述），至少选一项。默认 “视频”。
o	全局格式/质量：根据内容类型显示下拉菜单。免费用户高质量选项灰显。
	视频：具体分辨率/最佳/最低（后台智能匹配，始终合成带音频）。
	音频：具体比特率MP3/最佳可用原始格式（M4A或WEBM）。
	字幕：首选语言（优先原始，无则尝试最佳源语言的自动翻译）。不支持批量自动翻译和合并。
	评论：数量（100条，付费更多）、格式（XLSX/CSV）。
o	筛选条件 (仅对播放列表/频道生效)：可展开。含时长、上传日期、观看次数、标题关键词筛选器。仅当最大视频数据加载完毕后启用。多视频链接的批量任务不提供筛选。前端实时应用筛选，更新列表中符合条件数量。所有筛选条件之间是逻辑与关系。
o	下载目的地：单选按钮
	标准浏览器下载(默认)： 每个文件逐一触发下载对话框。
	增强本地文件夹保存： 利用File System Access API直接保存到用户选择目录。显示兼容性及授权提示。“选择本地目录”按钮。显示选定路径。保存路径：User_Selected_Directory/[源名称]或[x]_Videos_[任务ID]。默认勾选“为每个视频创建单独的子文件夹”，每个视频单独子文件夹，未勾选则所有文件平铺，文件名包含足够信息避免冲突。
	保存到网盘： Google Drive，Dropbox
o	并发控制： 提示“每个用户同时只能进行一个批量任务”。
•	任务启动与监控：
o	“开始批量下载”按钮：至少一个视频被选中且配置完成时可用。点击后锁定配置区，发送选定视频ID列表和配置到后端。后端验证权限、并发限制，标记父任务为“已启动”，并为每个选定视频的每项待处理内容创建批量子任务并入队。按钮变“处理中...”。
o	视频列表：每个视频项显示状态。当前处理的视频项可高亮显示。
o	整体任务进度摘要：列表上方显示总体进度（“正在处理X/Y”，“已完成A，失败B”）。
o	任务控制： 任务启动后显示“暂停”和“恢复”（暂停后，当前正在处理的子任务会尝试完成当前步骤，然后暂停接收新的子任务；恢复后继续）。“取消任务”（中止所有正在进行和排队中的子任务）。未完成的单个视频项提供“取消”。失败视频项提供“重试”。
•	任务完成：显示总结（总数、成功、失败、取消）。若选“增强本地文件夹保存”，提示本地目录路径。失败项提供“下载失败列表 (TXT)”。
3.4. 用户管理与任务历史
•	用户认证：注册（邮箱、密码、同意条款）、登录（邮箱、密码）、密码找回。
•	账户管理：用户头像下拉菜单访问“账户设置”页面，可修改密码、绑定/解绑邮箱、查看付费计划、升级/降级、删除账户。
•	“我的下载”页面：通过主导航访问，匿名用户也可访问。展示历史和进行中任务（单次任务/批量父任务）。每项含名称/类型/创建时间/状态/进度。支持筛选/排序。单次任务点击跳转回视频下载页并高亮显示任务面板，批量父任务点击跳转回批量下载页并显示最新状态。已完成任务可重下，失败任务可重试。页面顶部显示统计信息。匿名用户查看时提示任务记录的临时性，告知自动清理，引导注册/登录。
3.5. 后台管理面板：独立登录。
•	仪表盘：关键系统指标概览。
•	用户管理：列表、搜索、筛选用户。查看/编辑/禁用/启用/删除用户，更改付费计划。
•	任务管理：所有用户任务列表，筛选/排序。查看详情、日志。手动取消/重试任务。任务队列监控。
•	系统配置：管理代理池（添加/删除/编辑/健康检查），第三方API密钥与策略，平台限流，功能启用/禁用。
•	工作节点管理：监控每个节点运行状态、任务负载。手动启动/停止/重启/扩缩容。
•	日志与监控：结构化日志查询、关键性能指标监控、告警配置。
•	内容审查/版权：处理DMCA通知。黑名单管理（视频/频道ID），禁止下载特定内容。
3.6. 用户界面：风格简洁/现代/专业，统一配色/字体/图标。导航直观。响应式，主流桌面浏览器和移动设备一致优化体验。及时、清晰的视觉反馈。耗时操作界面响应。错误信息清晰、具体、非技术性。重要操作二次确认。付费功能通过灰显、图标、提示文字展示，不干扰用户体验。
4. 非功能性需求
4.1. 性能
•	响应：页面加载<3s (缓存时<1s)。链接解析<5s (缓存时<500ms)。批量任务创建<300ms。任务入队/启动<500ms。核心业务API<200ms。
•	任务处理： 单次下载在视频时长的1.5倍内完成（不含排队）。批量任务类似。1000条评论20秒内完成获取并生成文件。
•	并发： 支持至少500个并发活动用户。任务队列深度支持10000个任务。
4.2. 可靠性
•	服务运行时间： 平台整体 99.9% 。核心下载/处理 99.5%。非灾难性故障 10分钟内人工恢复。
•	弹性： 核心后端单节点。下载/处理工作节点可手动扩缩容。
•	任务成功率： 核心下载/处理任务 ≥ 95% 。可恢复错误2次自动重试（工作节点内部）。
•	数据完整性： 核心数据（用户、任务、付费）保证一致性、完整性。
•	依赖稳定性： yt-dlp和ffmpeg定期更新。管理多个第三方API。
4.3. 安全性
•	认证与授权： HttpOnly Secure Cookie会话认证。匿名ID平滑过渡注册。严格资源访问权限验证（UserId匹配）。后台独立强化认证。
•	数据传输： 强制HTTPS/TLS 1.2+。严格服务端/客户端验证、清理、转义。
•	滥用防治： IP/用户ID速率限制。CDN抵御DDoS/Bot。高风险操作或异常流量时启用验证码。
•	文件安全：临时文件存储在工作节点本地，非Web直接可访问路径。通过带签名的有时效性URL进行访问控制。过期后自动安全删除。
•	内部API安全：核心服务与工作节点之间的内部API部署在内部网络，可使用共享密钥或简单Token进行认证。
4.4. 可维护性
•	日志记录： 全面应用日志、任务执行日志、错误日志。结构化，便于查询。
•	监控/告警： 实时监控关键指标（CPU、内存、API响应时间、错误率、队列长度、代理池健康、存储空间），配置告警。
4.5. 法律与合规性
•	服务条款/隐私政策： 清晰发布，用户需同意。符合GDPR/CCPA。明确个人非商业用途。用户承担版权风险。
•	版权合规/DMCA： 不支持规避DRM。建立高效、透明DMCA处理流程，快速响应移除侵权内容。
•	数据保留/删除： 明确用户数据和临时文件保留策略。提供用户删除个人数据权利。不活跃匿名用户数据180天自动清理。
5. 架构设计
5.1. 组件详解
•	前端 (Next.js 15)：负责用户界面渲染、交互逻辑、路由管理、前端状态管理。调用后端核心服务API发起操作请求。通过WebSocket与核心服务建立连接，接收任务状态和进度的实时更新。处理文件下载（通过浏览器直接下载或File System Access API）。
•	核心服务 (ASP.NET Core 9 Minimal API)
o	API网关与业务处理： 接收前端HTTP请求，实现速率限制、输入验证。
o	同步解析协调： 当接收到前端的同步元数据解析请求时（如打开视频下载页或播放列表批量下载页首次加载），通过内部负载均衡选择一个可用工作节点，调用其内部HTTP API进行解析，接收工作节点解析结果并缓存，返回给前端。
o	任务管理与调度：用户发起下载/处理请求后，在PostgreSQL数据库中创建一条或多条任务记录，初始状态为“待处理”。根据系统负载和工作节点可用性，从数据库中分批获取“待处理”任务，封装成消息发送到RabbitMQ，并更新任务状态为“排队中”。
o	进度更新：提供WebSocket服务端，供前端连接以接收实时更新。维护用户ID到WebSocket连接的映射。提供内部HTTP API供工作节点定期汇报任务进度，收到后推送给对应前端。
o	文件下载授权： 接收前端文件下载请求，验证权限，查询文件所在工作节点IP和路径，生成带签名的临时下载URL（指向工作节点）返回前端。签名应包含文件标识、用户ID、过期时间戳，并使用HMAC算法生成。
o	代理池管理： 代理维护、健康检查。所有对YouTube的yt-dlp调用都必须获取代理。提供API供工作节点查询可用代理。每个代理IP限制5秒内最多1次请求。代理池目前100个代理，地址p.webshare.io:80，用户名vakzyshu-1到100，密码xx9skjnzudof。
o	数据库交互： 与PostgreSQL进行数据读写（用户账户、任务状态、配置等）。
•	消息队列 (RabbitMQ)：核心服务与工作节点之间的异步通信桥梁，实现任务负载均衡与解耦。不同任务类型配置不同队列实现优先级。
•	工作节点 (ASP.NET Core Worker Service)
o	同步解析服务： 暴露内部API接收核心服务同步解析请求，使用yt-dlp或付费API获取元数据并同步返回。
o	异步任务处理器： 持续从RabbitMQ中拉取任务消息，更新数据库中任务状态为处理中。根据任务类型执行操作：
	（可选）调用yt-dlp或付费API获取最新视频元数据或直接下载链接。对于批量任务中的多独立视频链接源，此时也是首次获取该视频的元数据，并会更新到数据库记录中。
	执行文件下载。调用ffmpeg进行音视频剪辑、格式转换、GIF制作、铃声制作等。
	执行过程中，每5秒调用核心服务内部API汇报进度（任务ID、用户ID、进度百分比和状态详情）。
	任务完成后/失败后，更新数据库中任务的状态和结果，并向RabbitMQ发送ack确认消息处理。
o	文件存储与服务： 将生成文件存储本地硬盘（24小时或按付费特权）。更新数据库任务记录的文件路径。处理用户带签名URL的文件下载请求（支持HTTP Range Requests）。定时扫描本地文件，删除已过期且关联任务已完成（或用户已下载）的文件。
o	容错处理： 消费RabbitMQ消息时采用手动ack模式。任务成功完成才发送ack。若工作节点处理中崩溃，未ack的消息会在超时后由RabbitMQ重新入队给其他工作节点处理。节点内部对可恢复错误进行2次的即时重试。任务消息中包含retry_count字段。若内部重试均失败，工作节点检查retry_count。若小于上限（如3次），则重新发布一条带递增retry_count的新消息到原队列，并ack当前消息。若达到重试上限，则将任务在数据库中标记为Failed，记录错误，并ack当前消息，不再重试。
o	节点下线流程： 管理员在后台面板下线工作节点时，节点进入“排空(Draining)”状态，停止接收新任务，但继续提供现有文件的下载服务。管理员需等待节点上所有文件均过期并被清理后，方可安全移除该节点。
•	数据库 (PostgreSQL 17)：持久化存储用户账户信息、任务数据、系统配置、代理池信息等。任务的状态和进度百分比直接写入任务表。
•	反向代理与CDN (Nginx & CDN)：Nginx作为反向代理，负责处理HTTPS、负载均衡（针对核心服务和工作节点的下载服务）、静态资源服务（Next.js的SSR/API流量）。Next.js生成的静态文件（JS/CSS/图片等）部署到CDN进行全球分发。
5.2. 技术要点
•	用户身份识别
o	匿名用户：首次访问生成UUID作为匿名ID，存入HttpOnly、Secure Cookie（有效期一年）。后端记录匿名用户，操作关联此ID。支持基于匿名 ID 的基础速率限制和滥用防治。不活跃匿名用户记录及其关联数据将在90-180天不活动后自动清理。
o	注册/登录用户：使用HttpOnly、Secure、SameSite=Lax的会话Cookie。所有操作关联其注册用户ID。
o	匿名到注册过渡： 用户在匿名状态下注册或登录时，后端识别匿名ID和新登录的注册用户ID。将数据库中与该匿名ID关联的所有未启动和进行中的临时任务数据、持久化任务记录等，更新其用户关联ID为注册用户ID。前端可删除匿名ID Cookie。
o	后端统一标识：统一Users表，包含匿名和注册用户。所有操作通过唯一UserId进行权限验证。
6. 数据库设计 (概要)：Users用户表、WorkerTasks执行任务表、BatchTasks批量父任务表
7. 接口设计 (概要)
8. 未来付费与邀请：用户付费订阅后解锁高级功能。用户分享链接吸引新用户。成功邀请达条件后，邀请者获奖励（如高级功能体验天数）。
