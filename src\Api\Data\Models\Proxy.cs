﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.Common;

namespace Api.Data.Models;

public class Proxy
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public required string Host { get; set; }
    public required int Port { get; set; }
    public required string Username { get; set; }
    public required string Password { get; set; }
    public ProxyType ProxyType { get; set; } = ProxyType.Http;
    public ProxyStatus Status { get; set; } = ProxyStatus.Active;
    public DateTime? LastUsedAt { get; set; }
    public DateTime? LastHealthCheckAt { get; set; }
    public ProxyHealthStatus HealthStatus { get; set; } = ProxyHealthStatus.Unknown;
    public int? ResponseTimeMs { get; set; }
    public int FailureCount { get; set; }
    public int SuccessCount { get; set; }
    public int UsageCount { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime CreatedAt { get; init; }
    public DateTime UpdatedAt { get; set; }
    public string? Notes { get; set; }
}

public class ProxyConfiguration : IEntityTypeConfiguration<Proxy>
{
    public void Configure(EntityTypeBuilder<Proxy> builder)
    {
        builder.ToTable("proxies", "public");
        builder.HasKey(ps => ps.Id);
        builder.Property(ps => ps.Id).ValueGeneratedNever();
        builder.Property(ps => ps.Host).IsRequired().HasMaxLength(255).HasColumnName("host");
        builder.Property(ps => ps.Port).IsRequired().HasColumnName("port");
        builder.Property(ps => ps.Username).IsRequired().HasMaxLength(100).HasColumnName("username");
        builder.Property(ps => ps.Password).IsRequired().HasMaxLength(255).HasColumnName("password");
        builder.Property(ps => ps.ProxyType).IsRequired().HasColumnName("proxy_type").HasDefaultValue(ProxyType.Http).HasConversion<string>();
        builder.Property(ps => ps.Status).IsRequired().HasColumnName("status").HasDefaultValue(ProxyStatus.Active).HasConversion<string>();
        builder.Property(ps => ps.LastUsedAt).HasColumnName("last_used_at");
        builder.Property(ps => ps.LastHealthCheckAt).HasColumnName("last_health_check_at");
        builder.Property(ps => ps.HealthStatus).IsRequired().HasColumnName("health_status").HasDefaultValue(ProxyHealthStatus.Unknown).HasConversion<string>();
        builder.Property(ps => ps.ResponseTimeMs).HasColumnName("response_time_ms");
        builder.Property(ps => ps.FailureCount).HasColumnName("failure_count").HasDefaultValue(0);
        builder.Property(ps => ps.SuccessCount).HasColumnName("success_count").HasDefaultValue(0);
        builder.Property(ps => ps.UsageCount).HasColumnName("usage_count").HasDefaultValue(0);
        builder.Property(ps => ps.ErrorMessage).HasMaxLength(500).HasColumnName("error_message");
        builder.Property(ps => ps.CreatedAt).IsRequired().HasColumnName("created_at").HasDefaultValueSql("CURRENT_TIMESTAMP");
        builder.Property(ps => ps.UpdatedAt).IsRequired().HasColumnName("updated_at").HasDefaultValueSql("CURRENT_TIMESTAMP");
        builder.Property(ps => ps.Notes).HasMaxLength(500).HasColumnName("notes");

        builder.HasIndex(ps => new { ps.Host, ps.Port, ps.Username }).IsUnique().HasDatabaseName("ix_proxies_unique_proxy");
        builder.HasIndex(ps => ps.Status).HasDatabaseName("ix_proxies_status");
        builder.HasIndex(ps => ps.HealthStatus).HasDatabaseName("ix_proxies_health_status");
        builder.HasIndex(ps => ps.LastUsedAt).HasDatabaseName("ix_proxies_last_used_at");
        builder.HasIndex(ps => ps.LastHealthCheckAt).HasDatabaseName("ix_proxies_last_health_check_at");
        builder.HasIndex(ps => new { ps.Status, ps.HealthStatus }).HasDatabaseName("ix_proxies_status_health");
        builder.HasIndex(ps => new { ps.Status, ps.LastUsedAt }).HasDatabaseName("ix_proxies_status_last_used");
    }
}