﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.Common;

namespace Api.Data.Models;

public class Worker
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public required string Name { get; set; }
    public required string BaseUrl { get; set; }
    public string? MachineName { get; set; }
    public int? CpuCores { get; set; }
    public double? TotalMemoryGB { get; set; }
    public double? TotalDiskGB { get; set; }
    public WorkerStatus Status { get; set; } = WorkerStatus.Offline;
    public WorkerHealthStatus HealthStatus { get; set; } = WorkerHealthStatus.Unknown;
    public int TotalProcessedTasks { get; set; }
    public int ConsecutiveFailures { get; set; }
    public DateTime CreatedAt { get; init; }
    public DateTime UpdatedAt { get; set; }
    public DateTime? LastActiveAt { get; set; }
    public DateTime? LastHealthCheckAt { get; set; }
    public DateTime? LastFailureAt { get; set; }
    public DateTime? LastStartAt { get; set; }

    public ICollection<WorkerMetrics> Metrics { get; set; } = [];
    public ICollection<WorkerAlert> Alerts { get; set; } = [];
    public ICollection<WorkerTask> AssignedTasks { get; set; } = [];
}

public class WorkerConfiguration : IEntityTypeConfiguration<Worker>
{
    public void Configure(EntityTypeBuilder<Worker> builder)
    {
        builder.ToTable("workers", "public");
        builder.HasKey(w => w.Id);
        builder.Property(w => w.Id).ValueGeneratedNever();
        builder.Property(w => w.Name).IsRequired().HasMaxLength(255).HasColumnName("name");
        builder.Property(w => w.BaseUrl).IsRequired().HasMaxLength(500).HasColumnName("base_url");
        builder.Property(w => w.MachineName).HasMaxLength(255).HasColumnName("machine_name");
        builder.Property(w => w.CpuCores).HasColumnName("cpu_cores");
        builder.Property(w => w.TotalMemoryGB).HasColumnName("total_memory_gb");
        builder.Property(w => w.TotalDiskGB).HasColumnName("total_disk_gb");
        builder.Property(w => w.Status).IsRequired().HasColumnName("status").HasDefaultValue(WorkerStatus.Offline);
        builder.Property(w => w.HealthStatus).IsRequired().HasColumnName("health_status").HasDefaultValue(WorkerHealthStatus.Unknown);
        builder.Property(w => w.TotalProcessedTasks).IsRequired().HasColumnName("total_processed_tasks").HasDefaultValue(0);
        builder.Property(w => w.ConsecutiveFailures).IsRequired().HasColumnName("consecutive_failures").HasDefaultValue(0);
        builder.Property(w => w.CreatedAt).IsRequired().HasColumnName("created_at").HasDefaultValueSql("CURRENT_TIMESTAMP");
        builder.Property(w => w.UpdatedAt).IsRequired().HasColumnName("updated_at").HasDefaultValueSql("CURRENT_TIMESTAMP");
        builder.Property(w => w.LastActiveAt).HasColumnName("last_active_at");
        builder.Property(w => w.LastHealthCheckAt).HasColumnName("last_health_check_at");
        builder.Property(w => w.LastFailureAt).HasColumnName("last_failure_at");
        builder.Property(w => w.LastStartAt).HasColumnName("last_start_at");

        builder.HasIndex(w => w.Name).HasDatabaseName("ix_workers_name");
        builder.HasIndex(w => w.BaseUrl).IsUnique().HasDatabaseName("ix_workers_base_url");
        builder.HasIndex(w => w.Status).HasDatabaseName("ix_workers_status");
        builder.HasIndex(w => w.HealthStatus).HasDatabaseName("ix_workers_health_status");
        builder.HasIndex(w => w.LastActiveAt).HasDatabaseName("ix_workers_last_active_at");
        builder.HasIndex(w => new { w.Status, w.HealthStatus }).HasDatabaseName("ix_workers_status_health");
    }
}