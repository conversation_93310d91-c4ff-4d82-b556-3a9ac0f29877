﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.Common;

namespace Api.Data.Models;

public class WorkerTask
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public required Guid UserId { get; set; }
    public Guid? BatchTaskId { get; set; }
    public required string Name { get; set; }
    public required WorkerTaskType TaskType { get; set; }
    public required string VideoId { get; set; }
    public string? VideoTitle { get; set; }
    public string? VideoUrl { get; set; }
    public required string Parameters { get; set; }
    public WorkerTaskStatus Status { get; set; } = WorkerTaskStatus.Pending;
    public int Progress { get; set; }
    public WorkerTaskPriority Priority { get; set; } = WorkerTaskPriority.Normal;
    public int RetryCount { get; set; }
    public Guid? WorkerId { get; set; }
    public string? OutputFormat { get; set; }
    public string? Quality { get; set; }
    public int? StartTime { get; set; }
    public int? EndTime { get; set; }
    public string? ResultPath { get; set; }
    public long? FileSize { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ErrorCode { get; set; }
    public DateTime CreatedAt { get; init; }
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public DateTime? FileExpiresAt { get; set; }

    public User User { get; set; } = null!;
    public BatchTask? BatchTask { get; set; }
    public Worker? Worker { get; set; }
}

public class WorkerTaskConfiguration : IEntityTypeConfiguration<WorkerTask>
{
    public void Configure(EntityTypeBuilder<WorkerTask> builder)
    {
        builder.ToTable("worker_tasks", "public");
        builder.HasKey(wt => wt.Id);
        builder.Property(wt => wt.Id).ValueGeneratedNever();
        builder.Property(wt => wt.UserId).IsRequired().HasColumnName("user_id");
        builder.Property(wt => wt.BatchTaskId).HasColumnName("batch_task_id");
        builder.Property(wt => wt.Name).IsRequired().HasMaxLength(255).HasColumnName("name");
        builder.Property(wt => wt.TaskType).IsRequired().HasColumnName("task_type").HasConversion<string>();
        builder.Property(wt => wt.VideoId).IsRequired().HasMaxLength(20).HasColumnName("video_id");
        builder.Property(wt => wt.VideoTitle).HasMaxLength(500).HasColumnName("video_title");
        builder.Property(wt => wt.VideoUrl).HasMaxLength(500).HasColumnName("video_url");
        builder.Property(wt => wt.Parameters).IsRequired().HasMaxLength(2000).HasColumnType("jsonb").HasColumnName("parameters");
        builder.Property(wt => wt.Status).IsRequired().HasColumnName("status").HasDefaultValue(WorkerTaskStatus.Pending).HasConversion<string>();
        builder.Property(wt => wt.Progress).HasColumnName("progress").HasDefaultValue(0);
        builder.Property(wt => wt.Priority).HasColumnName("priority").HasDefaultValue(WorkerTaskPriority.Normal).HasConversion<string>();
        builder.Property(wt => wt.RetryCount).HasColumnName("retry_count").HasDefaultValue(0);
        builder.Property(wt => wt.WorkerId).HasColumnName("worker_id");
        builder.Property(wt => wt.OutputFormat).HasMaxLength(50).HasColumnName("output_format");
        builder.Property(wt => wt.Quality).HasMaxLength(50).HasColumnName("quality");
        builder.Property(wt => wt.StartTime).HasColumnName("start_time");
        builder.Property(wt => wt.EndTime).HasColumnName("end_time");
        builder.Property(wt => wt.ResultPath).HasMaxLength(500).HasColumnName("result_path");
        builder.Property(wt => wt.FileSize).HasColumnName("file_size");
        builder.Property(wt => wt.ErrorMessage).HasMaxLength(1000).HasColumnName("error_message");
        builder.Property(wt => wt.ErrorCode).HasMaxLength(50).HasColumnName("error_code");
        builder.Property(wt => wt.CreatedAt).IsRequired().HasColumnName("created_at").HasDefaultValueSql("CURRENT_TIMESTAMP");
        builder.Property(wt => wt.StartedAt).HasColumnName("started_at");
        builder.Property(wt => wt.CompletedAt).HasColumnName("completed_at");
        builder.Property(wt => wt.UpdatedAt).IsRequired().HasColumnName("updated_at").HasDefaultValueSql("CURRENT_TIMESTAMP");
        builder.Property(wt => wt.FileExpiresAt).HasColumnName("file_expires_at");

        builder.HasIndex(wt => wt.UserId).HasDatabaseName("ix_worker_tasks_user_id");
        builder.HasIndex(wt => wt.BatchTaskId).HasDatabaseName("ix_worker_tasks_batch_task_id");
        builder.HasIndex(wt => wt.Status).HasDatabaseName("ix_worker_tasks_status");
        builder.HasIndex(wt => wt.TaskType).HasDatabaseName("ix_worker_tasks_task_type");
        builder.HasIndex(wt => wt.VideoId).HasDatabaseName("ix_worker_tasks_video_id");
        builder.HasIndex(wt => new { wt.Status, wt.Priority, wt.CreatedAt }).HasDatabaseName("ix_worker_tasks_queue_order");
        builder.HasIndex(wt => wt.WorkerId).HasDatabaseName("ix_worker_tasks_worker_id");
        builder.HasIndex(wt => wt.FileExpiresAt).HasDatabaseName("ix_worker_tasks_file_expires_at");
        builder.HasIndex(wt => new { wt.UserId, wt.Status, wt.CreatedAt }).HasDatabaseName("ix_worker_tasks_user_status_created");
        builder.HasIndex(wt => new { wt.BatchTaskId, wt.Status }).HasDatabaseName("ix_worker_tasks_batch_status");

        builder.HasOne(wt => wt.User).WithMany(u => u.WorkerTasks).HasForeignKey(wt => wt.UserId).OnDelete(DeleteBehavior.Cascade);
        builder.HasOne(wt => wt.BatchTask).WithMany(bt => bt.WorkerTasks).HasForeignKey(wt => wt.BatchTaskId).OnDelete(DeleteBehavior.Cascade);
        builder.HasOne(wt => wt.Worker).WithMany(w => w.AssignedTasks).HasForeignKey(wt => wt.WorkerId).OnDelete(DeleteBehavior.SetNull);
    }
}