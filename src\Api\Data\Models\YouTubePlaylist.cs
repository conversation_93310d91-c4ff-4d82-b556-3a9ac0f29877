﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Api.Data.Models;

public class YouTubePlaylist
{
    public required string PlaylistId { get; init; }
    public required string Title { get; set; }
    public string? Description { get; set; }
    public required string ChannelId { get; set; }
    public required string ChannelName { get; set; }
    public int VideoCount { get; set; }
    public string? Thumbnail { get; set; }
    public required string VideosJson { get; set; }
    public DateTime CreatedAt { get; init; }
    public DateTime UpdatedAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
}

public class YouTubePlaylistConfiguration : IEntityTypeConfiguration<YouTubePlaylist>
{
    public void Configure(EntityTypeBuilder<YouTubePlaylist> builder)
    {
        builder.ToTable("youtube_playlists", "public");
        builder.HasKey(p => p.PlaylistId);
        builder.Property(p => p.PlaylistId).IsRequired().HasMaxLength(50).HasColumnName("playlist_id");
        builder.Property(p => p.Title).IsRequired().HasMaxLength(500).HasColumnName("title");
        builder.Property(p => p.Description).HasColumnType("text").HasColumnName("description");
        builder.Property(p => p.ChannelId).IsRequired().HasMaxLength(50).HasColumnName("channel_id");
        builder.Property(p => p.ChannelName).IsRequired().HasMaxLength(255).HasColumnName("channel_name");
        builder.Property(p => p.VideoCount).HasColumnName("video_count");
        builder.Property(p => p.Thumbnail).HasMaxLength(500).HasColumnName("thumbnail");
        builder.Property(p => p.VideosJson).IsRequired().HasColumnType("jsonb").HasColumnName("videos_json");
        builder.Property(p => p.CreatedAt).IsRequired().HasColumnName("created_at").HasDefaultValueSql("CURRENT_TIMESTAMP");
        builder.Property(p => p.UpdatedAt).IsRequired().HasColumnName("updated_at").HasDefaultValueSql("CURRENT_TIMESTAMP");
        builder.Property(p => p.ExpiresAt).HasColumnName("expires_at");

        builder.HasIndex(p => p.ChannelId).HasDatabaseName("ix_youtube_playlists_channel_id");
        builder.HasIndex(p => p.ExpiresAt).HasDatabaseName("ix_youtube_playlists_expires_at");
        builder.HasIndex(p => p.CreatedAt).HasDatabaseName("ix_youtube_playlists_created_at");
        builder.HasIndex(p => new { p.ChannelId, p.VideoCount }).HasDatabaseName("ix_youtube_playlists_channel_videos");
    }
}