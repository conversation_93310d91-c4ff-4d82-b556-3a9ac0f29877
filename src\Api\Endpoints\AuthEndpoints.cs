using System.Security.Claims;
using Api.Data;
using Api.Data.Models;
using Api.Extensions;
using Api.Filters;
using Api.Services;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Shared.Common;
using Shared.DTOs;

namespace Api.Endpoints;

public static class AuthEndpoints
{
    public static void MapAuthEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/auth").WithTags("Auth");

        group.MapPost("/register", async (RegisterRequest request, AuthService authService, HttpContext context, ClaimsPrincipal principal) =>
        {
            var anonymousId = principal.FindFirstValue(ClaimTypes.NameIdentifier);
            Guid.TryParse(anonymousId, out var anonymousUserId);

            var result = await authService.RegisterAsync(request, anonymousUserId);
            if (!result.IsSuccess)
            {
                return result.ToHttpResult();
            }

            // 注册成功后，用户自动登录
            await context.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme); // 确保清除旧的匿名Cookie
            var user = result.Data!;
            await SignInUserAsync(context, user);
            await authService.RecordLoginSessionAsync(user.Id, context.Connection.RemoteIpAddress?.ToString(), context.Request.Headers.UserAgent.ToString());
            
            var userResponse = new UserResponse(user.Id, user.UserType, user.Email, user.PlanType, user.PlanExpiresAt, user.Status, user.CreatedAt, user.LastActiveAt);
            return Results.Ok(ApiResponse<UserResponse>.Success(userResponse));
        });

        group.MapPost("/login", async (LoginRequest request, AuthService authService, HttpContext context) =>
        {
            var ipAddress = context.Connection.RemoteIpAddress?.ToString();
            var result = await authService.LoginAsync(request, ipAddress);
            if (!result.IsSuccess)
            {
                return result.ToHttpResult();
            }

            var user = result.Data!;
            await SignInUserAsync(context, user);
            await authService.RecordLoginSessionAsync(user.Id, ipAddress, context.Request.Headers.UserAgent.ToString());

            var userResponse = new UserResponse(user.Id, user.UserType, user.Email, user.PlanType, user.PlanExpiresAt, user.Status, user.CreatedAt, user.LastActiveAt);
            var loginResponse = new LoginResponse(userResponse, "登录成功");
            return Results.Ok(ApiResponse<LoginResponse>.Success(loginResponse));
        });

        group.MapPost("/logout", async (HttpContext context, ClaimsPrincipal principal, AuthService authService, AppDbContext dbContext) =>
        {
            var userId = Guid.Parse(principal.FindFirstValue(ClaimTypes.NameIdentifier)!);
            // 登出时可以选择提升安全版本，强制所有其他设备也下线
            var user = await dbContext.Users.FindAsync(userId);
            if(user != null) await authService.IncrementSecurityVersionAsync(user);
            
            await context.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
            return Results.Ok(ApiResponse.Success());
        }).RequireAuthorization();

        group.MapGet("/me", (ClaimsPrincipal principal) =>
        {
            var userId = Guid.Parse(principal.FindFirstValue(ClaimTypes.NameIdentifier)!);
            var email = principal.FindFirstValue(ClaimTypes.Email);
            var userType = Enum.Parse<UserType>(principal.FindFirstValue("UserType")!);
            var planType = Enum.Parse<UserPlanType>(principal.FindFirstValue("PlanType")!);
            
            var currentUser = new CurrentUser(userId, userType, email, planType, userType == UserType.Anonymous);
            return Results.Ok(ApiResponse<CurrentUser>.Success(currentUser));
        }).RequireAuthorization();

        group.MapPost("/change-password", async (ChangePasswordRequest request, AuthService authService, ClaimsPrincipal principal) =>
        {
            var userId = Guid.Parse(principal.FindFirstValue(ClaimTypes.NameIdentifier)!);
            var result = await authService.ChangePasswordAsync(userId, request);
            return result.ToHttpResult();
        }).RequireAuthorization();
        
        group.MapPost("/forgot-password", async (ForgotPasswordRequest request, AuthService authService) =>
        {
            var result = await authService.ForgotPasswordAsync(request);
            return result.ToHttpResult();
        });

        group.MapPost("/reset-password", async (ResetPasswordRequest request, AuthService authService) =>
        {
            var result = await authService.ResetPasswordAsync(request);
            return result.ToHttpResult();
        });

        group.MapPost("/verify-email", async (VerifyEmailRequest request, AuthService authService) =>
        {
            var result = await authService.VerifyEmailAsync(request);
            return result.ToHttpResult();
        });
    }

    private static async Task SignInUserAsync(HttpContext context, User user)
    {
        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, user.Id.ToString()),
            new(ClaimTypes.Name, user.Email ?? string.Empty),
            new(ClaimTypes.Email, user.Email ?? string.Empty),
            new("UserType", user.UserType.ToString()),
            new("PlanType", user.PlanType.ToString()),
            new("ver", user.SecurityVersion.ToString()) // 添加安全版本 Claim
        };

        var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
        var authProperties = new AuthenticationProperties
        {
            IsPersistent = true,
            ExpiresUtc = DateTimeOffset.UtcNow.AddDays(30)
        };

        await context.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, new ClaimsPrincipal(claimsIdentity), authProperties);
    }
}