﻿using System.Text.RegularExpressions;
using System.Web;
using Api.Extensions;
using Api.Services;
using Shared.Common;
using Shared.DTOs;

namespace Api.Endpoints;

public static class YouTubeEndpoints
{
    public static void MapYouTubeEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/youtube").WithTags("YouTube");

        group.MapPost("/parse-url", (FetchUrlRequest request) =>
        {
            var fetchResult = FetchYouTubeUrl(request.Url);
            return ResultExtensions.ApiOk(fetchResult);
        }).WithSummary("解析YouTube URL").WithDescription("快速解析YouTube URL，提取视频ID、播放列表ID或频道ID");

        group.MapPost("/fetch", async Task<IResult> (FetchUrlWithWorkerRequest request, YouTubeService youTubeService) =>
        {
            var result = await youTubeService.FetchUrlAsync(request.Url, request.MaxVideos);
            return result.ToHttpResult();
        }).WithSummary("获取YouTube内容").WithDescription("使用工作节点获取YouTube URL的完整元数据信息");

        group.MapGet("/video/{videoId}", async Task<IResult> (string videoId, YouTubeService youTubeService) =>
        {
            var result = await youTubeService.GetVideoInfoAsync(videoId);
            return result.ToHttpResult();
        }).WithSummary("获取视频信息").WithDescription("获取指定视频ID的详细信息，包括视频流、音频流、字幕等");

        group.MapGet("/playlist/{playlistId}", async Task<IResult> (string playlistId, YouTubeService youTubeService, int? maxVideos = null) =>
        {
            var result = await youTubeService.GetPlaylistInfoAsync(playlistId, maxVideos);
            return result.ToHttpResult();
        }).WithSummary("获取播放列表信息").WithDescription("获取指定播放列表的详细信息和视频列表");

        group.MapGet("/channel/{channelId}", async Task<IResult> (string channelId, YouTubeService youTubeService, int? maxVideos = null) =>
        {
            var result = await youTubeService.GetChannelInfoAsync(channelId, maxVideos);
            return result.ToHttpResult();
        }).WithSummary("获取频道信息").WithDescription("获取指定频道的详细信息和最新视频列表");

        group.MapPost("/batch/videos", async Task<IResult> (BatchVideoRequest request, YouTubeService youTubeService) =>
        {
            var result = await youTubeService.GetBatchVideosAsync(request.VideoIds);
            return result.ToHttpResult();
        }).WithSummary("批量获取视频信息").WithDescription("批量获取多个视频的详细信息");

        group.MapGet("/formats/{videoId}", async Task<IResult> (string videoId, YouTubeService youTubeService) =>
        {
            var result = await youTubeService.GetVideoFormatsAsync(videoId);
            return result.ToHttpResult();
        }).WithSummary("获取视频格式").WithDescription("获取指定视频的所有可用格式和质量选项");

        group.MapPost("/refresh/{videoId}", async Task<IResult> (string videoId, YouTubeService youTubeService) =>
        {
            var result = await youTubeService.RefreshVideoInfoAsync(videoId);
            return result.ToHttpResult();
        }).WithSummary("刷新视频信息").WithDescription("强制刷新指定视频的元数据信息，忽略缓存");
    }

    private static FetchUrlResponse FetchYouTubeUrl(string url)
    {
        try
        {
            var uri = new Uri(url);

            // 检查是否为YouTube域名
            if (!IsYouTubeDomain(uri.Host)) return new FetchUrlResponse(YouTubeContentType.Video, null, null, null, "不是有效的YouTube URL");

            // 解析查询参数
            var query = HttpUtility.ParseQueryString(uri.Query);

            // 检查视频ID (v参数)
            var videoId = query["v"];
            if (!string.IsNullOrEmpty(videoId) && IsValidVideoId(videoId)) return new FetchUrlResponse(YouTubeContentType.Video, videoId, null, null, null);

            // 检查播放列表ID (list参数)
            var playlistId = query["list"];
            if (!string.IsNullOrEmpty(playlistId)) return new FetchUrlResponse(YouTubeContentType.Playlist, null, playlistId, null, null);

            // 检查频道URL模式
            var channelId = ExtractChannelId(uri.AbsolutePath);
            if (!string.IsNullOrEmpty(channelId)) return new FetchUrlResponse(YouTubeContentType.Channel, null, null, channelId, null);

            // 检查短链接格式 (youtu.be)
            if (uri.Host == "youtu.be" && uri.AbsolutePath.Length > 1)
            {
                var shortVideoId = uri.AbsolutePath.Substring(1);
                if (IsValidVideoId(shortVideoId)) return new FetchUrlResponse(YouTubeContentType.Video, shortVideoId, null, null, null);
            }

            return new FetchUrlResponse(YouTubeContentType.Video, null, null, null, "无法识别的YouTube URL格式");
        }
        catch (Exception)
        {
            return new FetchUrlResponse(YouTubeContentType.Video, null, null, null, "URL格式错误");
        }
    }

    private static bool IsYouTubeDomain(string host)
    {
        var youtubeDomains = new[] { "youtube.com", "www.youtube.com", "youtu.be", "m.youtube.com" };
        return youtubeDomains.Contains(host.ToLower());
    }

    private static bool IsValidVideoId(string videoId)
    {
        return YouTubeService.IsValidVideoId(videoId);
    }

    private static string? ExtractChannelId(string path)
    {
        // 匹配 /channel/UCxxxxx 格式
        var channelMatch = Regex.Match(path, @"/channel/([a-zA-Z0-9_-]+)");
        if (channelMatch.Success) return channelMatch.Groups[1].Value;

        // 匹配 /c/channelname 或 /user/username 格式
        var customMatch = Regex.Match(path, @"/(c|user)/([a-zA-Z0-9_-]+)");
        if (customMatch.Success) return customMatch.Groups[2].Value;

        return null;
    }
}