using System.Net;
using Shared.Common;

namespace Api.Extensions;

public static class ResultExtensions
{
    private static readonly IReadOnlyDictionary<ErrorType, (HttpStatusCode StatusCode, string DefaultMessage)> ErrorMapping =
        new Dictionary<ErrorType, (HttpStatusCode, string)>
        {
            [ErrorType.Internal] = (HttpStatusCode.InternalServerError, "发生意外的内部服务器错误"),
            [ErrorType.Validation] = (HttpStatusCode.UnprocessableEntity, "发生一个或多个验证错误"),
            [ErrorType.Unauthorized] = (HttpStatusCode.Unauthorized, "您无权执行此操作"),
            [ErrorType.Forbidden] = (HttpStatusCode.Forbidden, "您没有权限执行此操作"),
            [ErrorType.NotFound] = (HttpStatusCode.NotFound, "未找到请求的资源"),
            [ErrorType.Conflict] = (HttpStatusCode.Conflict, "资源当前状态发生冲突"),
            [ErrorType.TooManyRequests] = (HttpStatusCode.TooManyRequests, "请求过于频繁，请稍后重试"),
            [ErrorType.InvalidArgument] = (HttpStatusCode.BadRequest, "提供的一个或多个参数无效"),
            [ErrorType.InvalidOperation] = (HttpStatusCode.BadRequest, "当前状态下无法执行此操作"),
            [ErrorType.NotSupported] = (HttpStatusCode.BadRequest, "不支持的操作"),
            [ErrorType.Timeout] = (HttpStatusCode.RequestTimeout, "请求超时"),
            [ErrorType.OperationCancelled] = (HttpStatusCode.BadRequest, "操作已取消"),
            [ErrorType.NetworkError] = (HttpStatusCode.BadGateway, "网络连接错误"),
            [ErrorType.HttpError] = (HttpStatusCode.BadGateway, "HTTP请求错误"),
            [ErrorType.JsonError] = (HttpStatusCode.BadRequest, "JSON格式错误"),
            [ErrorType.InsufficientMemory] = (HttpStatusCode.InternalServerError, "内存不足"),

            [ErrorType.UserNotFound] = (HttpStatusCode.NotFound, "未找到指定的用户"),
            [ErrorType.UserEmailExists] = (HttpStatusCode.Conflict, "此邮箱地址已被使用"),
            [ErrorType.UserDisabled] = (HttpStatusCode.Forbidden, "用户账户已被禁用"),
            [ErrorType.UserQuotaExceeded] = (HttpStatusCode.Forbidden, "用户配额已超限"),
            [ErrorType.PlanQuotaExceeded] = (HttpStatusCode.Forbidden, "计划配额已超限"),

            [ErrorType.AuthTokenInvalid] = (HttpStatusCode.Unauthorized, "认证令牌无效"),
            [ErrorType.AuthTokenExpired] = (HttpStatusCode.Unauthorized, "认证令牌已过期"),
            [ErrorType.AuthCredentialsInvalid] = (HttpStatusCode.Unauthorized, "邮箱或密码无效"),

            [ErrorType.TaskNotFound] = (HttpStatusCode.NotFound, "未找到指定的任务"),
            [ErrorType.TaskAlreadyStarted] = (HttpStatusCode.Conflict, "任务已经开始"),
            [ErrorType.TaskAlreadyCompleted] = (HttpStatusCode.Conflict, "任务已经完成"),
            [ErrorType.TaskCancelled] = (HttpStatusCode.BadRequest, "任务已取消"),
            [ErrorType.TaskFailed] = (HttpStatusCode.BadRequest, "任务执行失败"),
            [ErrorType.TaskQueueFull] = (HttpStatusCode.ServiceUnavailable, "任务队列已满"),
            [ErrorType.UnsupportedTaskType] = (HttpStatusCode.BadRequest, "不支持的任务类型"),
            [ErrorType.BatchTaskNotFound] = (HttpStatusCode.NotFound, "未找到指定的批量任务"),

            [ErrorType.YouTubeVideoNotFound] = (HttpStatusCode.NotFound, "未找到YouTube视频"),
            [ErrorType.YouTubeVideoPrivate] = (HttpStatusCode.Forbidden, "YouTube视频为私有"),
            [ErrorType.YouTubeVideoUnavailable] = (HttpStatusCode.BadRequest, "YouTube视频不可用"),
            [ErrorType.YouTubePlaylistNotFound] = (HttpStatusCode.NotFound, "未找到YouTube播放列表"),
            [ErrorType.YouTubeChannelNotFound] = (HttpStatusCode.NotFound, "未找到YouTube频道"),
            [ErrorType.YouTubeRateLimited] = (HttpStatusCode.TooManyRequests, "YouTube API请求频率受限"),
            [ErrorType.YouTubeApiError] = (HttpStatusCode.BadGateway, "YouTube API错误"),
            [ErrorType.InvalidVideoId] = (HttpStatusCode.BadRequest, "无效的视频ID"),
            [ErrorType.InvalidPlaylistId] = (HttpStatusCode.BadRequest, "无效的播放列表ID"),
            [ErrorType.InvalidChannelId] = (HttpStatusCode.BadRequest, "无效的频道ID"),
            [ErrorType.InvalidUrl] = (HttpStatusCode.BadRequest, "无效的URL"),
            [ErrorType.InvalidUrlFormat] = (HttpStatusCode.BadRequest, "无效的URL格式"),

            [ErrorType.WorkerNotFound] = (HttpStatusCode.NotFound, "未找到工作节点"),
            [ErrorType.WorkerUnavailable] = (HttpStatusCode.ServiceUnavailable, "工作节点不可用"),
            [ErrorType.WorkerOverloaded] = (HttpStatusCode.ServiceUnavailable, "工作节点过载"),
            [ErrorType.WorkerProcessingError] = (HttpStatusCode.InternalServerError, "工作节点处理错误"),

            [ErrorType.FileNotFound] = (HttpStatusCode.NotFound, "未找到文件"),
            [ErrorType.FileTooLarge] = (HttpStatusCode.BadRequest, "文件过大"),
            [ErrorType.FileExpired] = (HttpStatusCode.Gone, "文件已过期"),
            [ErrorType.FileProcessingError] = (HttpStatusCode.InternalServerError, "文件处理错误"),
            [ErrorType.FileAccessDenied] = (HttpStatusCode.Forbidden, "文件访问被拒绝"),
            [ErrorType.DirectoryNotFound] = (HttpStatusCode.NotFound, "未找到目录"),

            [ErrorType.ProxyNotAvailable] = (HttpStatusCode.ServiceUnavailable, "代理不可用"),
            [ErrorType.ProxyConnectionFailed] = (HttpStatusCode.BadGateway, "代理连接失败"),
            [ErrorType.ProxyRateLimited] = (HttpStatusCode.TooManyRequests, "代理请求频率受限")
        };

    public static IResult ToHttpResult(this ServiceResult result, HttpStatusCode successStatusCode = HttpStatusCode.OK)
    {
        return result.IsSuccess ? CreateSuccessResult(successStatusCode) : ConvertErrorToHttpResult(result.Error, result.ValidationErrors);
    }

    public static IResult ToHttpResult<T>(this ServiceResult<T> result, HttpStatusCode successStatusCode = HttpStatusCode.OK)
    {
        return result.IsSuccess ? CreateSuccessResult(result.Data, successStatusCode) : ConvertErrorToHttpResult(result.Error, result.ValidationErrors);
    }

    private static IResult CreateSuccessResult(HttpStatusCode statusCode)
    {
        return statusCode switch
        {
            HttpStatusCode.OK => TypedResults.Ok(ApiResponse.Success()),
            HttpStatusCode.Created => TypedResults.Created(string.Empty, ApiResponse.Success()),
            HttpStatusCode.NoContent => TypedResults.NoContent(),
            _ => TypedResults.Ok(ApiResponse.Success())
        };
    }

    private static IResult CreateSuccessResult<T>(T? data, HttpStatusCode statusCode)
    {
        if (data is null) return TypedResults.NoContent();

        var response = ApiResponse<T>.Success(data);
        return statusCode switch
        {
            HttpStatusCode.OK => TypedResults.Ok(response),
            HttpStatusCode.Created => TypedResults.Created(string.Empty, response),
            _ => TypedResults.Ok(response)
        };
    }

    private static IResult ConvertErrorToHttpResult(Error error, List<ValidationError>? validationErrors)
    {
        var (statusCode, defaultMessage) = ErrorMapping.GetValueOrDefault(error.Type, (HttpStatusCode.BadRequest, "请求无效"));
        var errorDetails = new ErrorDetails(defaultMessage, error.Type.ToString(), validationErrors);
        var response = ApiResponse.Failure(errorDetails);

        return statusCode switch
        {
            HttpStatusCode.NotFound => TypedResults.NotFound(response),
            HttpStatusCode.Conflict => TypedResults.Conflict(response),
            HttpStatusCode.Forbidden => TypedResults.Json(response, statusCode: (int)HttpStatusCode.Forbidden),
            HttpStatusCode.Unauthorized => TypedResults.Unauthorized(),
            HttpStatusCode.UnprocessableEntity => TypedResults.UnprocessableEntity(response),
            HttpStatusCode.InternalServerError => TypedResults.Problem(response.Error?.Message, statusCode: (int)statusCode),
            _ => TypedResults.BadRequest(response)
        };
    }
}