using FluentValidation;
using Shared.Common;

namespace Api.Filters;

public class ValidationFilter<T> : IEndpointFilter where T : class
{
    public async ValueTask<object?> InvokeAsync(EndpointFilterInvocationContext context, EndpointFilterDelegate next)
    {
        var argument = context.Arguments.OfType<T>().FirstOrDefault();
        if (argument == null) return await next(context);

        var validator = context.HttpContext.RequestServices.GetService<IValidator<T>>();
        if (validator == null) return await next(context);

        var validationResult = await validator.ValidateAsync(argument);
        if (!validationResult.IsValid)
        {
            var errors = validationResult.Errors.Select(error => new ValidationError(error.PropertyName, error.ErrorMessage)).ToList();
            var errorDetails = new ErrorDetails("发生一个或多个验证错误", nameof(ErrorType.Validation), errors);
            return TypedResults.UnprocessableEntity(ApiResponse.Failure(errorDetails));
        }

        return await next(context);
    }
}

public static class ValidationFilterExtensions
{
    public static RouteHandlerBuilder WithValidation<TDto>(this RouteHandlerBuilder builder) where TDto : class
    {
        return builder.AddEndpointFilter<ValidationFilter<TDto>>();
    }
}