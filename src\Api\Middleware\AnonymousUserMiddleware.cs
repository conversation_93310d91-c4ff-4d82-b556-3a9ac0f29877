using System.Security.Claims;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Shared.Common;
using Shared.DTOs;

namespace Api.Middleware;

public class AnonymousUserMiddleware(RequestDelegate next, ILogger<AnonymousUserMiddleware> logger)
{
    private const string AnonymousUserCookieName = "YTDownloader.Anonymous";
    private static readonly TimeSpan AnonymousUserExpiry = TimeSpan.FromDays(30);

    public async Task InvokeAsync(HttpContext context)
    {
        if (context.User.Identity == null || !context.User.Identity.IsAuthenticated)
        {
            if (ShouldCreateAnonymousUser(context))
            {
                await CreateAnonymousUserAsync(context);
            }
        }

        await next(context);
    }

    private static bool ShouldCreateAnonymousUser(HttpContext context)
    {
        var path = context.Request.Path.Value?.ToLowerInvariant();

        if (string.IsNullOrEmpty(path))
            return false;

        var excludedPaths = new[]
        {
            "/health",
            "/api/health",
            "/favicon.ico",
            "/robots.txt",
            "/_next/",
            "/static/",
            "/assets/"
        };

        if (excludedPaths.Any(excluded => path.StartsWith(excluded)))
            return false;

        if (context.Request.Headers.ContainsKey(AnonymousUserCookieName))
            return false;

        return path.StartsWith("/api/") || path == "/";
    }

    private async Task CreateAnonymousUserAsync(HttpContext context)
    {
        var anonymousId = Guid.NewGuid().ToString();

        logger.LogDebug("为请求路径 {Path} 创建匿名用户身份: {AnonymousId}",
            context.Request.Path, anonymousId);

        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, anonymousId),
            new("UserType", UserType.Anonymous.ToString()),
            new("PlanType", UserPlanType.Free.ToString()),
            new("CreatedAt", DateTimeOffset.UtcNow.ToString("O"))
        };

        var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
        var claimsPrincipal = new ClaimsPrincipal(claimsIdentity);

        var authProperties = new AuthenticationProperties
        {
            IsPersistent = true,
            ExpiresUtc = DateTimeOffset.UtcNow.Add(AnonymousUserExpiry),
            AllowRefresh = false
        };

        context.Response.Cookies.Append(AnonymousUserCookieName, anonymousId, new CookieOptions
        {
            HttpOnly = true,
            Secure = context.Request.IsHttps,
            SameSite = SameSiteMode.Lax,
            Expires = DateTimeOffset.UtcNow.Add(AnonymousUserExpiry)
        });

        await context.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, claimsPrincipal, authProperties);
        context.User = claimsPrincipal;
    }
}


