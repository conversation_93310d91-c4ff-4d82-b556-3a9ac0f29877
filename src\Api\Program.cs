using System.Security.Claims;
using Api.Data;
using Api.Endpoints;
using Api.Middleware;
using Api.Scheduled;
using Api.Services;
using FluentValidation;
using MassTransit;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.EntityFrameworkCore;
using Scalar.AspNetCore;
using Shared.Common;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddDbContext<AppDbContext>(options => options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));
builder.Services.AddValidatorsFromAssemblyContaining<Program>();
builder.Services.AddExceptionHandler<GlobalExceptionHandler>();
builder.Services.AddProblemDetails();

builder.Services.AddScoped<AuthService>();
builder.Services.AddScoped<UserService>();
builder.Services.AddScoped<EmailService>();
builder.Services.AddScoped<CacheService>();
builder.Services.AddScoped<HealthCheckService>();
builder.Services.AddScoped<YouTubeService>();
builder.Services.AddScoped<ProxyService>();
builder.Services.AddScoped<WorkerService>();
builder.Services.AddScoped<TaskService>();
builder.Services.AddScoped<TaskPublishService>();
builder.Services.AddScoped<FileDownloadService>();
builder.Services.AddScoped<AdminAuthService>();

builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme).AddCookie(options =>
{
    options.Cookie.Name = "YTDownloader.Auth";
    options.Cookie.HttpOnly = true;
    options.Cookie.SameSite = SameSiteMode.Lax;
    options.Cookie.SecurePolicy = CookieSecurePolicy.SameAsRequest;
    options.ExpireTimeSpan = TimeSpan.FromDays(7);
    options.SlidingExpiration = true;
    options.Cookie.IsEssential = true;

    options.Events.OnRedirectToLogin = context =>
    {
        context.Response.StatusCode = StatusCodes.Status401Unauthorized;
        return Task.CompletedTask;
    };
    options.Events.OnRedirectToAccessDenied = context =>
    {
        context.Response.StatusCode = StatusCodes.Status403Forbidden;
        return Task.CompletedTask;
    };
    options.Events.OnValidatePrincipal = async context =>
    {
        var userIdString = context.Principal?.FindFirstValue(ClaimTypes.NameIdentifier);
        var versionString = context.Principal?.FindFirstValue("ver");
        if (!Guid.TryParse(userIdString, out var userId) || !int.TryParse(versionString, out var cookieVersion))
        {
            context.RejectPrincipal();
            await context.HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
            return;
        }

        var dbContext = context.HttpContext.RequestServices.GetRequiredService<AppDbContext>();
        var user = await dbContext.Users.AsNoTracking().FirstOrDefaultAsync(u => u.Id == userId);
        var isAccountLocked = user?.AccountLockedUntil.HasValue == true && user.AccountLockedUntil.Value > DateTime.UtcNow;
        if (user == null || user.SecurityVersion > cookieVersion || user.Status != UserAccountStatus.Active || isAccountLocked)
        {
            context.RejectPrincipal();
            await context.HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
        }
    };
});
builder.Services.AddAuthorization();

builder.Services.AddHttpClient<WorkerService>(client =>
{
    client.Timeout = TimeSpan.FromSeconds(60);
    client.DefaultRequestHeaders.Add("User-Agent", "YTDownloader-Api/1.0");
});
builder.Services.AddHttpClient<YouTubeService>(client =>
{
    client.Timeout = TimeSpan.FromSeconds(30);
    client.DefaultRequestHeaders.Add("User-Agent", "YTDownloader-Api/1.0");
});

builder.Services.AddMemoryCache();

builder.Services.AddMassTransit(x =>
{
    x.AddConsumer<TaskResultConsumer>();
    x.AddConsumer<TaskProgressConsumer>();

    x.UsingRabbitMq((context, cfg) =>
    {
        cfg.Host(builder.Configuration.GetConnectionString("RabbitMQ") ?? "amqp://guest:guest@localhost:5672/");
        cfg.ReceiveEndpoint("task-result-queue", e =>
        {
            e.ConfigureConsumer<TaskResultConsumer>(context);
            e.Durable = true;
        });
        cfg.ReceiveEndpoint("task-progress-queue", e =>
        {
            e.ConfigureConsumer<TaskProgressConsumer>(context);
            e.Durable = true;
        });
        cfg.ConfigureEndpoints(context);
    });
});

builder.Services.AddHostedService<CleanupService>();
builder.Services.AddHostedService<ProxyHealthCheckService>();
builder.Services.AddHostedService<TaskSchedulerService>();

builder.Services.AddOpenApi();

if (builder.Environment.IsDevelopment())
    builder.Services.AddCors(options =>
    {
        options.AddDefaultPolicy(policy =>
        {
            policy.WithOrigins("http://localhost:3000", "https://localhost:3000").AllowAnyMethod().AllowAnyHeader().AllowCredentials();
        });
    });

var app = builder.Build();
app.UseExceptionHandler();
/*
using (var scope = app.Services.CreateScope())
{
    var logger = app.Services.GetRequiredService<ILogger<Program>>();

    // 启动健康检查
    var startupHealthCheckService = scope.ServiceProvider.GetRequiredService<HealthCheckService>();
    var healthCheckResult = await startupHealthCheckService.PerformStartupHealthCheckAsync();

    if (!healthCheckResult.IsSuccess)
    {
        logger.LogCritical("启动健康检查失败: {Error}", healthCheckResult.ErrorMessage);
        throw new InvalidOperationException($"启动健康检查失败: {healthCheckResult.ErrorMessage}");
    }

    logger.LogInformation("应用启动验证完成，所有检查通过");
}
*/

if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
    app.MapScalarApiReference();
    app.UseCors();
}

app.UseMiddleware<AnonymousUserMiddleware>();
app.UseAuthentication();
app.UseAuthorization();

// 用户端点
app.MapAuthEndpoints();
app.MapYouTubeEndpoints();
app.MapTaskEndpoints();
app.MapFileEndpoints();
app.MapBillingEndpoints();

// 管理员端点
app.MapAdminAuthEndpoints();
app.MapAdminDashboardEndpoints();
app.MapAdminUserEndpoints();
app.MapAdminTaskEndpoints();
app.MapAdminWorkerEndpoints();
app.MapAdminProxyEndpoints();
app.MapAdminContentEndpoints();

app.MapGet("/", () => Results.Ok(new
{
    status = "Backend is running",
    timestamp = DateTime.UtcNow
})).WithTags("Health");

app.Run();