using Shared.Common;

namespace Api.Services;

public class AdminAuthService
{
    public async Task<ServiceResult<AdminLoginResponse>> LoginAsync(AdminLoginRequest request)
    {
        await Task.Delay(1);
        return ServiceResult<AdminLoginResponse>.Failure("功能暂未实现");
    }

    public async Task<ServiceResult> LogoutAsync(HttpContext context)
    {
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }

    public async Task<ServiceResult<AdminAuthStatusResponse>> CheckAuthStatusAsync(HttpContext context)
    {
        await Task.Delay(1);
        return ServiceResult<AdminAuthStatusResponse>.Failure("功能暂未实现");
    }
}