using Api.Data;
using Api.Data.Models;
using Microsoft.EntityFrameworkCore;
using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class AuthService(AppDbContext dbContext, ILogger<AuthService> logger, EmailService emailService)
{
    public async Task<ServiceResult<User>> RegisterAsync(RegisterRequest request, Guid? anonymousUserId = null)
    {
        var existingUser = await dbContext.Users.FirstOrDefaultAsync(u => u.Email == request.Email.ToLower() && u.UserType == UserType.Registered);
        if (existingUser != null) return ServiceResult<User>.Failure(new Error(ErrorType.UserEmailExists, "该邮箱已被注册"));

        User? userToUpdate = null;
        if (anonymousUserId.HasValue)
            userToUpdate = await dbContext.Users.FirstOrDefaultAsync(u => u.Id == anonymousUserId.Value && u.UserType == UserType.Anonymous);

        if (userToUpdate != null)
        {
            // “升级”匿名用户为注册用户
            userToUpdate.UserType = UserType.Registered;
            userToUpdate.Email = request.Email.ToLower();
            userToUpdate.PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.Password);
            userToUpdate.UpdatedAt = DateTime.UtcNow;
            userToUpdate.LastActiveAt = DateTime.UtcNow;
            // 安全版本在升级时也递增
            userToUpdate.SecurityVersion++;
        }
        else
        {
            // 创建一个全新的注册用户
            userToUpdate = new User
            {
                UserType = UserType.Registered,
                Email = request.Email.ToLower(),
                PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.Password),
                PlanType = UserPlanType.Free,
                Status = UserAccountStatus.Active,
                CreatedAt = DateTime.UtcNow,
                LastActiveAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                SecurityVersion = 1
            };
            dbContext.Users.Add(userToUpdate);
        }

        await dbContext.SaveChangesAsync();

        // 省略了邮件发送逻辑以保持简洁

        logger.LogInformation("用户注册成功: {Email}", request.Email);
        return ServiceResult<User>.Success(userToUpdate);
    }

    public async Task<ServiceResult<User>> LoginAsync(LoginRequest request)
    {
        var user = await dbContext.Users.FirstOrDefaultAsync(u => u.Email == request.Email.ToLower() && u.UserType == UserType.Registered);

        if (user == null || user.PasswordHash == null || !BCrypt.Net.BCrypt.Verify(request.Password, user.PasswordHash))
            return ServiceResult<User>.Failure(new Error(ErrorType.AuthCredentialsInvalid, "邮箱或密码错误"));

        if (user.Status != UserAccountStatus.Active) return ServiceResult<User>.Failure(new Error(ErrorType.UserDisabled, "账户已被禁用"));

        user.LastActiveAt = DateTime.UtcNow;
        await dbContext.SaveChangesAsync();

        logger.LogInformation("用户登录成功: {Email}", request.Email);
        return ServiceResult<User>.Success(user);
    }

    public async Task<ServiceResult> ChangePasswordAsync(Guid userId, ChangePasswordRequest request)
    {
        var user = await dbContext.Users.FindAsync(userId);
        if (user == null || user.PasswordHash == null) return ServiceResult.Failure(new Error(ErrorType.UserNotFound));

        if (!BCrypt.Net.BCrypt.Verify(request.CurrentPassword, user.PasswordHash))
            return ServiceResult.Failure(new Error(ErrorType.AuthCredentialsInvalid, "当前密码错误"));

        user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.NewPassword);

        // 关键：密码修改后，提升安全版本号
        await IncrementSecurityVersionAsync(user);

        await dbContext.SaveChangesAsync();

        logger.LogInformation("用户 {UserId} 密码修改成功", userId);
        return ServiceResult.Success();
    }

    public async Task<ServiceResult> ResetPasswordAsync(ResetPasswordRequest request)
    {
        // 此处省略了Token验证逻辑以保持简洁，实际应包含
        var user = await dbContext.Users.FirstOrDefaultAsync(u => u.Email == request.Email.ToLower());
        if (user == null) return ServiceResult.Failure(new Error(ErrorType.UserNotFound));

        user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.NewPassword);

        // 关键：密码重置后，提升安全版本号
        await IncrementSecurityVersionAsync(user);

        await dbContext.SaveChangesAsync();

        logger.LogInformation("用户 {UserId} 密码重置成功", user.Id);
        return ServiceResult.Success();
    }

    /// <summary>
    ///     提升用户的安全版本号。这是实现会话撤销的核心。
    /// </summary>
    public async Task IncrementSecurityVersionAsync(User user)
    {
        user.SecurityVersion++;
        user.UpdatedAt = DateTime.UtcNow;
        await dbContext.SaveChangesAsync();
    }

    /// <summary>
    ///     记录一次成功的登录事件。
    ///     这个方法只用于审计和向用户展示会话列表，不参与认证过程。
    /// </summary>
    public async Task RecordLoginSessionAsync(Guid userId, string? ipAddress, string? userAgent)
    {
        var session = new UserSession
        {
            UserId = userId,
            IpAddress = ipAddress,
            UserAgent = userAgent,
            CreatedAt = DateTime.UtcNow,
            ExpiresAt = DateTime.UtcNow.AddDays(30) // 与Cookie的过期时间保持一致
        };

        dbContext.UserSessions.Add(session);
        await dbContext.SaveChangesAsync();

        logger.LogInformation("已为用户 {UserId} 记录新的登录会话: {SessionId}", userId, session.Id);
    }

  
}