namespace Api.Services;

public class EmailService(ILogger<EmailService> logger, IConfiguration configuration)
{
    public Task SendEmailAsync(string to, string subject, string body)
    {
        logger.LogInformation("将向 {To} 发送邮件，主题: {Subject}", to, subject);
        return Task.CompletedTask;
    }

    public async Task SendEmailVerificationAsync(string email, string token)
    {
        var baseUrl = configuration["App:BaseUrl"] ?? "http://localhost:3000";
        var verificationUrl = $"{baseUrl}/verify-email?email={Uri.EscapeDataString(email)}&token={Uri.EscapeDataString(token)}";

        var subject = "验证您的邮箱地址";
        var body = $@"
            <h2>欢迎注册 YouTube 下载站！</h2>
            <p>请点击下面的链接验证您的邮箱地址：</p>
            <p><a href=""{verificationUrl}"" style=""background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;"">验证邮箱</a></p>
            <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
            <p>{verificationUrl}</p>
            <p>此链接将在24小时后过期。</p>
            <p>如果您没有注册账户，请忽略此邮件。</p>
        ";

        await SendEmailAsync(email, subject, body);
        logger.LogInformation("已向 {Email} 发送邮箱验证邮件", email);
    }

    public async Task SendPasswordResetAsync(string email, string token)
    {
        var baseUrl = configuration["App:BaseUrl"] ?? "http://localhost:3000";
        var resetUrl = $"{baseUrl}/reset-password?email={Uri.EscapeDataString(email)}&token={Uri.EscapeDataString(token)}";

        var subject = "重置您的密码";
        var body = $@"
            <h2>密码重置请求</h2>
            <p>我们收到了您的密码重置请求。请点击下面的链接重置您的密码：</p>
            <p><a href=""{resetUrl}"" style=""background-color: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;"">重置密码</a></p>
            <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
            <p>{resetUrl}</p>
            <p>此链接将在1小时后过期。</p>
            <p>如果您没有请求重置密码，请忽略此邮件。您的密码不会被更改。</p>
        ";

        await SendEmailAsync(email, subject, body);
        logger.LogInformation("已向 {Email} 发送密码重置邮件", email);
    }

    public async Task SendPasswordResetEmailAsync(string email, string token)
    {
        await SendPasswordResetAsync(email, token);
    }
}