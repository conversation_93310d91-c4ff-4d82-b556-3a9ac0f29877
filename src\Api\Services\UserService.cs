using System.Security.Claims;
using Api.Data;
using Api.Data.Models;
using Microsoft.EntityFrameworkCore;
using Shared.Common;

namespace Api.Services;

public class UserService(AppDbContext dbContext, ILogger<UserService> logger)
{
    /// <summary>
    /// 确保由 ClaimsPrincipal 代表的用户在数据库中存在。
    /// 如果用户是匿名的且在数据库中不存在，则“即时”创建该用户。
    /// </summary>
    /// <param name="principal">代表当前用户的 ClaimsPrincipal。</param>
    /// <returns>数据库中的 User 实体。</returns>
    /// <exception cref="InvalidOperationException">当 principal 无效时抛出。</exception>
    public async Task<User> GetOrCreateUserAsync(ClaimsPrincipal principal)
    {
        var userIdString = principal.FindFirstValue(ClaimTypes.NameIdentifier);
        if (!Guid.TryParse(userIdString, out var userId))
        {
            throw new InvalidOperationException("ClaimsPrincipal is missing a valid user ID.");
        }

        var user = await dbContext.Users.FindAsync(userId);
        if (user != null)
        {
            return user;
        }

        var userTypeString = principal.FindFirstValue("UserType");
        if (userTypeString == UserType.Anonymous.ToString())
        {
            var planTypeString = principal.FindFirstValue("PlanType");
            Enum.TryParse<UserPlanType>(planTypeString, out var planType);

            var newUser = new User
            {
                Id = userId, // 【关键】使用来自 Cookie 的 ID，而不是生成新的
                UserType = UserType.Anonymous,
                PlanType = planType,
                Status = UserAccountStatus.Active,
                CreatedAt = DateTime.UtcNow,
                LastActiveAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            dbContext.Users.Add(newUser);
            await dbContext.SaveChangesAsync();

            logger.LogInformation("Just-in-time creation of anonymous user: {UserId}", userId);
            return newUser;
        }

        // 如果数据库中找不到用户，但他/她又不是我们能创建的匿名用户，
        // 这意味着这是一个无效的用户状态（例如，用户已被删除但Cookie仍然存在）。
        throw new InvalidOperationException($"User with ID {userId} not found in database and cannot be created.");
    }
}