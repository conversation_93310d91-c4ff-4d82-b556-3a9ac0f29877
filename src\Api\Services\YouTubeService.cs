using System.Text.Json;
using System.Text.RegularExpressions;
using Api.Data;
using Api.Data.Models;
using Microsoft.EntityFrameworkCore;
using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class YouTubeService
{
    private readonly CacheService _cacheService;
    private readonly AppDbContext _dbContext;
    private readonly TimeSpan _defaultExpiration = TimeSpan.FromMinutes(30);
    private readonly ILogger<YouTubeService> _logger;
    private readonly WorkerService _workerService;

    public YouTubeService(AppDbContext dbContext, ILogger<YouTubeService> logger, WorkerService workerService, CacheService cacheService)
    {
        _dbContext = dbContext;
        _logger = logger;
        _workerService = workerService;
        _cacheService = cacheService;
    }

    public async Task<ServiceResult<YouTubeVideoResponse>> GetVideoInfoAsync(string videoId)
    {
        try
        {
            var cacheKey = GetVideoKey(videoId);
            var cached = await _cacheService.GetAsync<YouTubeVideoResponse>(cacheKey);
            if (cached != null)
            {
                _logger.LogDebug("返回视频 {VideoId} 的缓存信息", videoId);
                return ServiceResult<YouTubeVideoResponse>.Success(cached);
            }

            try
            {
                var dbResult = await GetVideoFromDatabaseAsync(videoId);
                if (dbResult != null)
                {
                    await _cacheService.SetAsync(cacheKey, dbResult);
                    _logger.LogDebug("从数据库获取视频 {VideoId} 信息并更新缓存", videoId);
                    return ServiceResult<YouTubeVideoResponse>.Success(dbResult);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "从数据库获取视频信息时发生错误: {VideoId}", videoId);
            }

            var workerResult = await _workerService.ParseVideoAsync(videoId);
            if (!workerResult.IsSuccess)
                return ServiceResult<YouTubeVideoResponse>.Failure(workerResult.ErrorMessage!, workerResult.ErrorCode);

            var cacheTask = _cacheService.SetAsync(cacheKey, workerResult.Data!);
            var dbTask = Task.Run(async () =>
            {
                try
                {
                    await SaveVideoToDatabaseAsync(workerResult.Data!);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "保存视频 {VideoId} 到数据库时发生错误", videoId);
                }
            });

            _ = Task.WhenAll(cacheTask, dbTask).ContinueWith(t =>
            {
                if (t.IsFaulted)
                    _logger.LogWarning(t.Exception, "保存视频 {VideoId} 元数据时发生错误", videoId);
                else
                    _logger.LogDebug("已保存视频 {VideoId} 元数据到缓存和数据库", videoId);
            }, TaskContinuationOptions.ExecuteSynchronously);

            _logger.LogInformation("成功获取视频 {VideoId} 信息", videoId);
            return ServiceResult<YouTubeVideoResponse>.Success(workerResult.Data!);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取视频 {VideoId} 信息时发生错误", videoId);
            return ServiceResult<YouTubeVideoResponse>.Failure("获取视频信息时发生错误", "INTERNAL_ERROR");
        }
    }

    public async Task<ServiceResult<YouTubePlaylistResponse>> GetPlaylistInfoAsync(string playlistId, int? maxVideos = null)
    {
        try
        {
            var cacheKey = GetPlaylistKey(playlistId);
            var cached = await _cacheService.GetAsync<YouTubePlaylistResponse>(cacheKey);
            if (cached != null)
            {
                _logger.LogDebug("返回播放列表 {PlaylistId} 的缓存信息", playlistId);
                return ServiceResult<YouTubePlaylistResponse>.Success(cached);
            }

            try
            {
                var dbResult = await GetPlaylistFromDatabaseAsync(playlistId);
                if (dbResult != null)
                {
                    await _cacheService.SetAsync(cacheKey, dbResult);
                    _logger.LogDebug("从数据库获取播放列表 {PlaylistId} 信息并更新缓存", playlistId);
                    return ServiceResult<YouTubePlaylistResponse>.Success(dbResult);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "从数据库获取播放列表信息时发生错误: {PlaylistId}", playlistId);
            }

            var workerResult = await _workerService.ParsePlaylistAsync(playlistId, maxVideos);
            if (!workerResult.IsSuccess)
                return ServiceResult<YouTubePlaylistResponse>.Failure(workerResult.ErrorMessage!, workerResult.ErrorCode);

            var cacheTask = _cacheService.SetAsync(cacheKey, workerResult.Data!);
            var dbTask = Task.Run(async () =>
            {
                try
                {
                    await SavePlaylistToDatabaseAsync(workerResult.Data!);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "保存播放列表 {PlaylistId} 到数据库时发生错误", playlistId);
                }
            });

            _ = Task.WhenAll(cacheTask, dbTask).ContinueWith(t =>
            {
                if (t.IsFaulted)
                    _logger.LogWarning(t.Exception, "保存播放列表 {PlaylistId} 元数据时发生错误", playlistId);
                else
                    _logger.LogDebug("已保存播放列表 {PlaylistId} 元数据到缓存和数据库", playlistId);
            }, TaskContinuationOptions.ExecuteSynchronously);

            _logger.LogInformation("成功获取播放列表 {PlaylistId} 信息", playlistId);
            return ServiceResult<YouTubePlaylistResponse>.Success(workerResult.Data!);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取播放列表 {PlaylistId} 信息时发生错误", playlistId);
            return ServiceResult<YouTubePlaylistResponse>.Failure("获取播放列表信息时发生错误", "INTERNAL_ERROR");
        }
    }

    public async Task<ServiceResult<YouTubeChannelResponse>> GetChannelInfoAsync(string channelId, int? maxVideos = null)
    {
        try
        {
            var cacheKey = GetChannelKey(channelId);
            var cached = await _cacheService.GetAsync<YouTubeChannelResponse>(cacheKey);
            if (cached != null)
            {
                _logger.LogDebug("返回频道 {ChannelId} 的缓存信息", channelId);
                return ServiceResult<YouTubeChannelResponse>.Success(cached);
            }

            try
            {
                var dbResult = await GetChannelFromDatabaseAsync(channelId);
                if (dbResult != null)
                {
                    await _cacheService.SetAsync(cacheKey, dbResult);
                    _logger.LogDebug("从数据库获取频道 {ChannelId} 信息并更新缓存", channelId);
                    return ServiceResult<YouTubeChannelResponse>.Success(dbResult);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "从数据库获取频道信息时发生错误: {ChannelId}", channelId);
            }

            var workerResult = await _workerService.ParseChannelAsync(channelId, maxVideos);
            if (!workerResult.IsSuccess)
                return ServiceResult<YouTubeChannelResponse>.Failure(workerResult.ErrorMessage!, workerResult.ErrorCode);

            var cacheTask = _cacheService.SetAsync(cacheKey, workerResult.Data!);
            var dbTask = Task.Run(async () =>
            {
                try
                {
                    await SaveChannelToDatabaseAsync(workerResult.Data!);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "保存频道 {ChannelId} 到数据库时发生错误", channelId);
                }
            });

            _ = Task.WhenAll(cacheTask, dbTask).ContinueWith(t =>
            {
                if (t.IsFaulted)
                    _logger.LogWarning(t.Exception, "保存频道 {ChannelId} 元数据时发生错误", channelId);
                else
                    _logger.LogDebug("已保存频道 {ChannelId} 元数据到缓存和数据库", channelId);
            }, TaskContinuationOptions.ExecuteSynchronously);

            _logger.LogInformation("成功获取频道 {ChannelId} 信息", channelId);
            return ServiceResult<YouTubeChannelResponse>.Success(workerResult.Data!);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取频道 {ChannelId} 信息时发生错误", channelId);
            return ServiceResult<YouTubeChannelResponse>.Failure("获取频道信息时发生错误", "INTERNAL_ERROR");
        }
    }

    public async Task<ServiceResult<YouTubeUrlFetchResult>> FetchUrlAsync(string url, int? maxVideos = null)
    {
        try
        {
            var videoId = ExtractVideoIdFromUrl(url);
            if (videoId != null)
            {
                var videoResult = await GetVideoInfoAsync(videoId);
                if (videoResult.IsSuccess)
                    return ServiceResult<YouTubeUrlFetchResult>.Success(new YouTubeUrlFetchResult(YouTubeContentType.Video, videoResult.Data, null, null));
            }

            var playlistId = ExtractPlaylistIdFromUrl(url);
            if (playlistId != null)
            {
                var playlistResult = await GetPlaylistInfoAsync(playlistId, maxVideos);
                if (playlistResult.IsSuccess)
                    return ServiceResult<YouTubeUrlFetchResult>.Success(new YouTubeUrlFetchResult(YouTubeContentType.Playlist, null, playlistResult.Data,
                        null));
            }

            var channelId = ExtractChannelIdFromUrl(url);
            if (channelId != null)
            {
                var channelResult = await GetChannelInfoAsync(channelId, maxVideos);
                if (channelResult.IsSuccess)
                    return ServiceResult<YouTubeUrlFetchResult>.Success(new YouTubeUrlFetchResult(YouTubeContentType.Channel, null, null, channelResult.Data));
            }

            return ServiceResult<YouTubeUrlFetchResult>.Failure("无法识别的YouTube URL格式", "INVALID_URL_FORMAT");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing YouTube URL: {Url}", url);
            return ServiceResult<YouTubeUrlFetchResult>.Failure("获取YouTube URL信息时发生错误", "INTERNAL_ERROR");
        }
    }

    public static string? ExtractVideoIdFromUrl(string url)
    {
        if (string.IsNullOrWhiteSpace(url)) return null;

        var patterns = new[]
        {
            @"(?:youtube\.com\/watch\?v=|youtu\.be\/|m\.youtube\.com\/watch\?v=)([a-zA-Z0-9_-]{11})",
            @"youtube\.com\/embed\/([a-zA-Z0-9_-]{11})",
            @"youtube\.com\/v\/([a-zA-Z0-9_-]{11})"
        };

        foreach (var pattern in patterns)
        {
            var match = Regex.Match(url, pattern, RegexOptions.IgnoreCase);
            if (match.Success) return match.Groups[1].Value;
        }

        return IsValidVideoId(url) ? url : null;
    }

    public static string? ExtractPlaylistIdFromUrl(string url)
    {
        if (string.IsNullOrWhiteSpace(url)) return null;

        var match = Regex.Match(url, @"[?&]list=([a-zA-Z0-9_-]+)", RegexOptions.IgnoreCase);
        if (match.Success) return match.Groups[1].Value;

        return IsValidPlaylistId(url) ? url : null;
    }

    public static string? ExtractChannelIdFromUrl(string url)
    {
        if (string.IsNullOrWhiteSpace(url)) return null;

        var patterns = new[]
        {
            @"youtube\.com\/channel\/([a-zA-Z0-9_-]+)",
            @"youtube\.com\/c\/([a-zA-Z0-9_-]+)",
            @"youtube\.com\/user\/([a-zA-Z0-9_-]+)",
            @"youtube\.com\/@([a-zA-Z0-9_-]+)"
        };

        foreach (var pattern in patterns)
        {
            var match = Regex.Match(url, pattern, RegexOptions.IgnoreCase);
            if (match.Success) return match.Groups[1].Value;
        }

        return IsValidChannelId(url) ? url : null;
    }

    private static string GetVideoKey(string videoId)
    {
        return $"video:{videoId}";
    }

    private static string GetPlaylistKey(string playlistId)
    {
        return $"playlist:{playlistId}";
    }

    private static string GetChannelKey(string channelId)
    {
        return $"channel:{channelId}";
    }

    public static bool IsValidVideoId(string videoId)
    {
        return !string.IsNullOrWhiteSpace(videoId) && Regex.IsMatch(videoId, @"^[a-zA-Z0-9_-]{11}$");
    }

    public static bool IsValidPlaylistId(string playlistId)
    {
        return !string.IsNullOrWhiteSpace(playlistId) && Regex.IsMatch(playlistId, @"^[a-zA-Z0-9_-]+$") && playlistId.Length >= 10;
    }

    public static bool IsValidChannelId(string channelId)
    {
        return !string.IsNullOrWhiteSpace(channelId) && Regex.IsMatch(channelId, @"^[a-zA-Z0-9_-]+$") && channelId.Length >= 3;
    }

    private async Task<YouTubeVideoResponse?> GetVideoFromDatabaseAsync(string videoId)
    {
        var video = await _dbContext.YouTubeVideos.FirstOrDefaultAsync(v => v.VideoId == videoId);

        if (video == null)
        {
            _logger.LogDebug("数据库中未找到视频: {VideoId}", videoId);
            return null;
        }

        if (video.ExpiresAt.HasValue && video.ExpiresAt.Value <= DateTime.UtcNow)
        {
            _logger.LogDebug("视频数据已过期: {VideoId}, 过期时间: {ExpiresAt}", videoId, video.ExpiresAt);
            return null;
        }

        var response = MapToVideoResponse(video);
        _logger.LogDebug("从数据库获取视频信息: {VideoId}", videoId);
        return response;
    }

    private async Task SaveVideoToDatabaseAsync(YouTubeVideoResponse videoResponse, TimeSpan? expiration = null)
    {
        var actualExpiration = expiration ?? _defaultExpiration;
        var expiresAt = DateTime.UtcNow.Add(actualExpiration);

        var existingVideo = await _dbContext.YouTubeVideos.FirstOrDefaultAsync(v => v.VideoId == videoResponse.VideoId);

        if (existingVideo != null)
        {
            UpdateVideoEntity(existingVideo, videoResponse, expiresAt);
            existingVideo.UpdatedAt = DateTime.UtcNow;
        }
        else
        {
            var newVideo = CreateVideoEntity(videoResponse, expiresAt);
            _dbContext.YouTubeVideos.Add(newVideo);
        }

        await _dbContext.SaveChangesAsync();
        _logger.LogDebug("已保存视频信息到数据库: {VideoId}", videoResponse.VideoId);
    }

    private async Task<YouTubePlaylistResponse?> GetPlaylistFromDatabaseAsync(string playlistId)
    {
        var playlist = await _dbContext.YouTubePlaylists.FirstOrDefaultAsync(p => p.PlaylistId == playlistId);

        if (playlist == null)
        {
            _logger.LogDebug("数据库中未找到播放列表: {PlaylistId}", playlistId);
            return null;
        }

        if (playlist.ExpiresAt.HasValue && playlist.ExpiresAt.Value <= DateTime.UtcNow)
        {
            _logger.LogDebug("播放列表数据已过期: {PlaylistId}, 过期时间: {ExpiresAt}", playlistId, playlist.ExpiresAt);
            return null;
        }

        var response = MapToPlaylistResponse(playlist);
        _logger.LogDebug("从数据库获取播放列表信息: {PlaylistId}", playlistId);
        return response;
    }

    private async Task SavePlaylistToDatabaseAsync(YouTubePlaylistResponse playlistResponse, TimeSpan? expiration = null)
    {
        var actualExpiration = expiration ?? _defaultExpiration;
        var expiresAt = DateTime.UtcNow.Add(actualExpiration);

        var existingPlaylist = await _dbContext.YouTubePlaylists.FirstOrDefaultAsync(p => p.PlaylistId == playlistResponse.PlaylistId);

        if (existingPlaylist != null)
        {
            UpdatePlaylistEntity(existingPlaylist, playlistResponse, expiresAt);
            existingPlaylist.UpdatedAt = DateTime.UtcNow;
        }
        else
        {
            var newPlaylist = CreatePlaylistEntity(playlistResponse, expiresAt);
            _dbContext.YouTubePlaylists.Add(newPlaylist);
        }

        await _dbContext.SaveChangesAsync();
        _logger.LogDebug("已保存播放列表信息到数据库: {PlaylistId}", playlistResponse.PlaylistId);
    }

    private async Task<YouTubeChannelResponse?> GetChannelFromDatabaseAsync(string channelId)
    {
        var channel = await _dbContext.YouTubeChannels.FirstOrDefaultAsync(c => c.ChannelId == channelId);

        if (channel == null)
        {
            _logger.LogDebug("数据库中未找到频道: {ChannelId}", channelId);
            return null;
        }

        if (channel.ExpiresAt.HasValue && channel.ExpiresAt.Value <= DateTime.UtcNow)
        {
            _logger.LogDebug("频道数据已过期: {ChannelId}, 过期时间: {ExpiresAt}", channelId, channel.ExpiresAt);
            return null;
        }

        var response = MapToChannelResponse(channel);
        _logger.LogDebug("从数据库获取频道信息: {ChannelId}", channelId);
        return response;
    }

    private async Task SaveChannelToDatabaseAsync(YouTubeChannelResponse channelResponse, TimeSpan? expiration = null)
    {
        var actualExpiration = expiration ?? _defaultExpiration;
        var expiresAt = DateTime.UtcNow.Add(actualExpiration);

        var existingChannel = await _dbContext.YouTubeChannels.FirstOrDefaultAsync(c => c.ChannelId == channelResponse.ChannelId);

        if (existingChannel != null)
        {
            UpdateChannelEntity(existingChannel, channelResponse, expiresAt);
            existingChannel.UpdatedAt = DateTime.UtcNow;
        }
        else
        {
            var newChannel = CreateChannelEntity(channelResponse, expiresAt);
            _dbContext.YouTubeChannels.Add(newChannel);
        }

        await _dbContext.SaveChangesAsync();
        _logger.LogDebug("已保存频道信息到数据库: {ChannelId}", channelResponse.ChannelId);
    }

    private YouTubeVideoResponse MapToVideoResponse(YouTubeVideo video)
    {
        var videoStreams = JsonSerializer.Deserialize<List<VideoStreamInfo>>(video.VideoStreamsJson) ?? [];
        var audioStreams = JsonSerializer.Deserialize<List<AudioStreamInfo>>(video.AudioStreamsJson) ?? [];
        var subtitles = JsonSerializer.Deserialize<List<SubtitleInfo>>(video.SubtitlesJson) ?? [];

        return new YouTubeVideoResponse(video.VideoId, video.Title, video.Description, video.ChannelId, video.ChannelName, video.Duration, video.ViewCount,
            video.LikeCount, video.CommentCount, video.UploadDate, video.Thumbnail, videoStreams, audioStreams, subtitles);
    }

    private YouTubePlaylistResponse MapToPlaylistResponse(YouTubePlaylist playlist)
    {
        var videos = JsonSerializer.Deserialize<List<PlaylistVideoInfo>>(playlist.VideosJson) ?? [];

        return new YouTubePlaylistResponse(playlist.PlaylistId, playlist.Title, playlist.Description, playlist.ChannelId, playlist.ChannelName,
            playlist.VideoCount, playlist.Thumbnail, videos);
    }

    private YouTubeChannelResponse MapToChannelResponse(YouTubeChannel channel)
    {
        var videos = JsonSerializer.Deserialize<List<ChannelVideoInfo>>(channel.VideosJson) ?? [];

        return new YouTubeChannelResponse(channel.ChannelId, channel.Title, channel.Description, channel.SubscriberCount, channel.VideoCount, channel.Thumbnail,
            videos);
    }

    private YouTubeVideo CreateVideoEntity(YouTubeVideoResponse response, DateTime expiresAt)
    {
        return new YouTubeVideo
        {
            VideoId = response.VideoId,
            Title = response.Title,
            Description = response.Description,
            ChannelId = response.ChannelId,
            ChannelName = response.ChannelName,
            Duration = response.Duration,
            ViewCount = response.ViewCount,
            LikeCount = response.LikeCount,
            CommentCount = response.CommentCount,
            UploadDate = response.UploadDate,
            Thumbnail = response.Thumbnail,
            VideoStreamsJson = JsonSerializer.Serialize(response.VideoStreams),
            AudioStreamsJson = JsonSerializer.Serialize(response.AudioStreams),
            SubtitlesJson = JsonSerializer.Serialize(response.Subtitles),
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            ExpiresAt = expiresAt
        };
    }

    private void UpdateVideoEntity(YouTubeVideo entity, YouTubeVideoResponse response, DateTime expiresAt)
    {
        entity.Title = response.Title;
        entity.Description = response.Description;
        entity.ChannelName = response.ChannelName;
        entity.Duration = response.Duration;
        entity.ViewCount = response.ViewCount;
        entity.LikeCount = response.LikeCount;
        entity.CommentCount = response.CommentCount;
        entity.UploadDate = response.UploadDate;
        entity.Thumbnail = response.Thumbnail;
        entity.VideoStreamsJson = JsonSerializer.Serialize(response.VideoStreams);
        entity.AudioStreamsJson = JsonSerializer.Serialize(response.AudioStreams);
        entity.SubtitlesJson = JsonSerializer.Serialize(response.Subtitles);
        entity.ExpiresAt = expiresAt;
    }

    private YouTubePlaylist CreatePlaylistEntity(YouTubePlaylistResponse response, DateTime expiresAt)
    {
        return new YouTubePlaylist
        {
            PlaylistId = response.PlaylistId,
            Title = response.Title,
            Description = response.Description,
            ChannelId = response.ChannelId,
            ChannelName = response.ChannelName,
            VideoCount = response.VideoCount,
            Thumbnail = response.Thumbnail,
            VideosJson = JsonSerializer.Serialize(response.Videos),
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            ExpiresAt = expiresAt
        };
    }

    private void UpdatePlaylistEntity(YouTubePlaylist entity, YouTubePlaylistResponse response, DateTime expiresAt)
    {
        entity.Title = response.Title;
        entity.Description = response.Description;
        entity.ChannelName = response.ChannelName;
        entity.VideoCount = response.VideoCount;
        entity.Thumbnail = response.Thumbnail;
        entity.VideosJson = JsonSerializer.Serialize(response.Videos);
        entity.ExpiresAt = expiresAt;
    }

    private YouTubeChannel CreateChannelEntity(YouTubeChannelResponse response, DateTime expiresAt)
    {
        return new YouTubeChannel
        {
            ChannelId = response.ChannelId,
            Title = response.Title,
            Description = response.Description,
            SubscriberCount = response.SubscriberCount,
            VideoCount = response.VideoCount,
            Thumbnail = response.Thumbnail,
            VideosJson = JsonSerializer.Serialize(response.Videos),
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            ExpiresAt = expiresAt
        };
    }

    private void UpdateChannelEntity(YouTubeChannel entity, YouTubeChannelResponse response, DateTime expiresAt)
    {
        entity.Title = response.Title;
        entity.Description = response.Description;
        entity.SubscriberCount = response.SubscriberCount;
        entity.VideoCount = response.VideoCount;
        entity.Thumbnail = response.Thumbnail;
        entity.VideosJson = JsonSerializer.Serialize(response.Videos);
        entity.ExpiresAt = expiresAt;
    }

    // ==================== 新增方法 ====================

    public async Task<ServiceResult<List<YouTubeVideoResponse>>> GetBatchVideosAsync(List<string> videoIds)
    {
        try
        {
            var results = new List<YouTubeVideoResponse>();

            foreach (var videoId in videoIds)
            {
                var result = await GetVideoInfoAsync(videoId);
                if (result.IsSuccess)
                    results.Add(result.Data!);
                else
                    _logger.LogWarning("获取视频 {VideoId} 信息失败: {Error}", videoId, result.ErrorMessage);
            }

            return ServiceResult<List<YouTubeVideoResponse>>.Success(results);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量获取视频信息时发生错误");
            return ServiceResult<List<YouTubeVideoResponse>>.Failure("批量获取视频信息失败", "BATCH_FETCH_ERROR");
        }
    }

    public async Task<ServiceResult<YouTubeVideoResponse>> RefreshVideoInfoAsync(string videoId)
    {
        try
        {
            // 清除缓存
            var cacheKey = GetVideoKey(videoId);
            await _cacheService.RemoveAsync(cacheKey);

            // 从数据库删除
            var existingVideo = await _dbContext.YouTubeVideos.FirstOrDefaultAsync(v => v.VideoId == videoId);
            if (existingVideo != null)
            {
                _dbContext.YouTubeVideos.Remove(existingVideo);
                await _dbContext.SaveChangesAsync();
            }

            // 重新获取
            return await GetVideoInfoAsync(videoId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "刷新视频 {VideoId} 信息时发生错误", videoId);
            return ServiceResult<YouTubeVideoResponse>.Failure("刷新视频信息失败", "REFRESH_ERROR");
        }
    }

    public async Task<ServiceResult<VideoFormatsResponse>> GetVideoFormatsAsync(string videoId)
    {
        try
        {
            var videoResult = await GetVideoInfoAsync(videoId);
            if (!videoResult.IsSuccess) return ServiceResult<VideoFormatsResponse>.Failure(videoResult.ErrorMessage!, videoResult.ErrorCode);

            var video = videoResult.Data!;
            var formatsResponse = new VideoFormatsResponse(video.VideoId, video.VideoStreams, video.AudioStreams, video.Subtitles);

            return ServiceResult<VideoFormatsResponse>.Success(formatsResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取视频 {VideoId} 格式信息时发生错误", videoId);
            return ServiceResult<VideoFormatsResponse>.Failure("获取视频格式失败", "FORMATS_ERROR");
        }
    }
}