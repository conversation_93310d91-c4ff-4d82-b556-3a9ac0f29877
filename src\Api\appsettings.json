{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=yt_downloader_db;Username=postgres;Password=******", "RabbitMQ": "amqp://guest:guest@localhost:5672/"}, "App": {"BaseUrl": "http://localhost:3000"}, "YtDlp": {"ExecutablePath": "yt-dlp"}, "RabbitMQ": {"HostName": "localhost", "Port": "5672", "UserName": "guest", "Password": "guest", "VirtualHost": "/"}, "Monitoring": {"AdminEmail": "<EMAIL>"}, "NodeAuthentication": {"ApiKey": "worker-node-secret-key-2024"}}