namespace Shared.Common;

public record ApiResponse(ErrorDetails? Error = null)
{
    public bool IsSuccess => Error is null;

    public static ApiResponse Failure(ErrorDetails error)
    {
        return new ApiResponse(error);
    }

    public static ApiResponse Success()
    {
        return new ApiResponse();
    }
}

public record ApiResponse<T>(T? Data, ErrorDetails? Error = null) : ApiResponse(Error)
{
    public new static ApiResponse<T> Failure(ErrorDetails error)
    {
        return new ApiResponse<T>(default, error);
    }

    public static ApiResponse<T> Success(T data)
    {
        return new ApiResponse<T>(data);
    }
}

public record ErrorDetails(string Message, string? Code = null, List<ValidationError>? ValidationErrors = null);