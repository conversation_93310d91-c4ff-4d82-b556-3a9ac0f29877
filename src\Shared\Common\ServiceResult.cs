namespace Shared.Common;

public readonly record struct Error(ErrorType Type, string? DeveloperMessage = null)
{
    public static readonly Error None = new(ErrorType.None);
}

public record ServiceResult(Error Error, List<ValidationError>? ValidationErrors = null)
{
    private ServiceResult() : this(Error.None)
    {
    }

    public bool IsSuccess => Error.Type == ErrorType.None;

    public static ServiceResult Failure(Error error)
    {
        if (error.Type == ErrorType.None) throw new ArgumentException("不能使用None类型的错误创建失败结果", nameof(error));
        return new ServiceResult(error);
    }

    public static ServiceResult Success()
    {
        return new ServiceResult();
    }

    public static ServiceResult ValidationError(List<ValidationError> errors)
    {
        return new ServiceResult(new Error(ErrorType.Validation, "输入验证失败"), errors);
    }
}

public record ServiceResult<T>(T? Data, Error Error, List<ValidationError>? ValidationErrors = null) : ServiceResult(Error, ValidationErrors)
{
    public new static ServiceResult<T> Failure(Error error)
    {
        if (error.Type == ErrorType.None) throw new ArgumentException("不能使用None类型的错误创建失败结果", nameof(error));
        return new ServiceResult<T>(default, error);
    }

    public static ServiceResult<T> Success(T data)
    {
        return new ServiceResult<T>(data, Error.None);
    }

    public new static ServiceResult<T> ValidationError(List<ValidationError> errors)
    {
        return new ServiceResult<T>(default, new Error(ErrorType.Validation, "输入验证失败"), errors);
    }

    public static implicit operator ServiceResult<T>(T data)
    {
        return new ServiceResult<T>(data, Error.None);
    }
}

public record ValidationError(string Field, string Message);