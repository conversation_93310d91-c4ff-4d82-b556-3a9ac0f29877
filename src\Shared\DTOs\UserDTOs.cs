using FluentValidation;

namespace Shared.DTOs;

public record RegisterRequest(string Email, string Password);

public class RegisterRequestValidator : AbstractValidator<RegisterRequest>
{
    public RegisterRequestValidator()
    {
        RuleFor(x => x.Email).NotEmpty().WithMessage("邮箱不能为空").EmailAddress().WithMessage("邮箱格式不正确").MaximumLength(255).WithMessage("邮箱长度不能超过255个字符");
        RuleFor(x => x.Password).NotEmpty().WithMessage("密码不能为空").MinimumLength(6).WithMessage("密码至少6个字符").MaximumLength(32).WithMessage("密码不能超过32个字符");
    }
}

public record LoginRequest(string Email, string Password);

public class LoginRequestValidator : AbstractValidator<LoginRequest>
{
    public LoginRequestValidator()
    {
        RuleFor(x => x.Email).NotEmpty().WithMessage("邮箱不能为空").EmailAddress().WithMessage("邮箱格式不正确");
        RuleFor(x => x.Password).NotEmpty().WithMessage("密码不能为空");
    }
}

public record ChangePasswordRequest(string CurrentPassword, string NewPassword);

public class ChangePasswordRequestValidator : AbstractValidator<ChangePasswordRequest>
{
    public ChangePasswordRequestValidator()
    {
        RuleFor(x => x.CurrentPassword).NotEmpty().WithMessage("当前密码不能为空");
        RuleFor(x => x.NewPassword).NotEmpty().WithMessage("新密码不能为空").MinimumLength(6).WithMessage("新密码至少6个字符").MaximumLength(32).WithMessage("新密码不能超过32个字符")
            .NotEqual(x => x.CurrentPassword).WithMessage("新密码不能与当前密码相同");
    }
}

public record ForgotPasswordRequest(string Email);

public class ForgotPasswordRequestValidator : AbstractValidator<ForgotPasswordRequest>
{
    public ForgotPasswordRequestValidator()
    {
        RuleFor(x => x.Email).NotEmpty().WithMessage("邮箱不能为空").EmailAddress().WithMessage("邮箱格式不正确");
    }
}

public record ResetPasswordRequest(string Email, string Token, string NewPassword);

public class ResetPasswordRequestValidator : AbstractValidator<ResetPasswordRequest>
{
    public ResetPasswordRequestValidator()
    {
        RuleFor(x => x.Email).NotEmpty().WithMessage("邮箱不能为空").EmailAddress().WithMessage("邮箱格式不正确");
        RuleFor(x => x.Token).NotEmpty().WithMessage("重置令牌不能为空");
        RuleFor(x => x.NewPassword).NotEmpty().WithMessage("新密码不能为空").MinimumLength(6).WithMessage("新密码至少6个字符").MaximumLength(32).WithMessage("新密码不能超过32个字符");
    }
}

public record VerifyEmailRequest(string Email, string Token);

public class VerifyEmailRequestValidator : AbstractValidator<VerifyEmailRequest>
{
    public VerifyEmailRequestValidator()
    {
        RuleFor(x => x.Email).NotEmpty().WithMessage("邮箱不能为空").EmailAddress().WithMessage("邮箱格式不正确");
        RuleFor(x => x.Token).NotEmpty().WithMessage("验证令牌不能为空");
    }
}