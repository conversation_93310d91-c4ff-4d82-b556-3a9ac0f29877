using FluentValidation;
using Shared.Common;

namespace Shared.DTOs;

public static class YouTubeConstants
{
    public const int MAX_VIDEOS_LIMIT = 1000;
    public const int MIN_VIDEOS = 1;
}

public static class YouTubeUrlValidator
{
    public static bool IsValidYouTubeUrl(string url)
    {
        if (string.IsNullOrEmpty(url)) return false;
        return url.Contains("youtube.com") || url.Contains("youtu.be");
    }
}

public record FetchVideoRequest(string VideoId, ProxyInfo? Proxy);

public class FetchVideoRequestValidator : AbstractValidator<FetchVideoRequest>
{
    public FetchVideoRequestValidator()
    {
        RuleFor(x => x.VideoId).NotEmpty().WithMessage("视频ID不能为空").Matches(@"^[a-zA-Z0-9_-]{11}$").WithMessage("视频ID格式不正确");
    }
}

public record FetchPlaylistRequest(string PlaylistId, ProxyInfo? Proxy, int? MaxVideos = null);

public class FetchPlaylistRequestValidator : AbstractValidator<FetchPlaylistRequest>
{
    public FetchPlaylistRequestValidator()
    {
        RuleFor(x => x.PlaylistId).NotEmpty().WithMessage("播放列表ID不能为空");

        RuleFor(x => x.MaxVideos).InclusiveBetween(YouTubeConstants.MIN_VIDEOS, YouTubeConstants.MAX_VIDEOS_LIMIT).When(x => x.MaxVideos.HasValue)
            .WithMessage("最大视频数量必须在1-1000之间");
    }
}

public record FetchChannelRequest(string ChannelId, ProxyInfo? Proxy, int? MaxVideos = null);

public class FetchChannelRequestValidator : AbstractValidator<FetchChannelRequest>
{
    public FetchChannelRequestValidator()
    {
        RuleFor(x => x.ChannelId).NotEmpty().WithMessage("频道ID不能为空");

        RuleFor(x => x.MaxVideos).InclusiveBetween(YouTubeConstants.MIN_VIDEOS, YouTubeConstants.MAX_VIDEOS_LIMIT).When(x => x.MaxVideos.HasValue)
            .WithMessage("最大视频数量必须在1-1000之间");
    }
}

public record FetchUrlRequest(string Url);

public class FetchUrlRequestValidator : AbstractValidator<FetchUrlRequest>
{
    public FetchUrlRequestValidator()
    {
        RuleFor(x => x.Url).NotEmpty().WithMessage("URL不能为空").Must(YouTubeUrlValidator.IsValidYouTubeUrl).WithMessage("必须是有效的YouTube URL");
    }
}

public record FetchUrlWithWorkerRequest(string Url, int? MaxVideos = null);

public class FetchUrlWithWorkerRequestValidator : AbstractValidator<FetchUrlWithWorkerRequest>
{
    public FetchUrlWithWorkerRequestValidator()
    {
        RuleFor(x => x.Url).NotEmpty().WithMessage("URL不能为空").Must(YouTubeUrlValidator.IsValidYouTubeUrl).WithMessage("必须是有效的YouTube URL");

        RuleFor(x => x.MaxVideos).InclusiveBetween(YouTubeConstants.MIN_VIDEOS, YouTubeConstants.MAX_VIDEOS_LIMIT).When(x => x.MaxVideos.HasValue)
            .WithMessage("最大视频数量必须在1-1000之间");
    }
}

public record ThumbnailInfo(string Url, int? Width, int? Height);

public record VideoStreamInfo(
    string FormatId,
    string Quality,
    string? Resolution,
    int? Width,
    int? Height,
    float? Fps,
    long? FileSize,
    string Format,
    string? Codec,
    bool HasAudio,
    string Url);

public record AudioStreamInfo(string FormatId, string Quality, int? Bitrate, long? FileSize, string Format, string? Codec, string Url);

public record SubtitleInfo(string Language, string LanguageName, bool IsAutoGenerated, string Url);

public record VideoFormat(string FormatId, string Extension, string Quality, long FileSize, string Url);

public record PlaylistVideoInfo(string Id, string Title, string ChannelTitle, int Duration, string ThumbnailUrl, DateTime PublishedAt, int Position);

public record ChannelVideoInfo(string Id, string Title, int Duration, int ViewCount, string ThumbnailUrl, DateTime PublishedAt);

public record YouTubeVideoResponse(
    string VideoId,
    string Title,
    string? Description,
    string ChannelId,
    string ChannelName,
    int Duration,
    long? ViewCount,
    long? LikeCount,
    long? CommentCount,
    DateTime? UploadDate,
    string? Thumbnail,
    List<VideoStreamInfo> VideoStreams,
    List<AudioStreamInfo> AudioStreams,
    List<SubtitleInfo> Subtitles);

public record YouTubePlaylistResponse(
    string PlaylistId,
    string Title,
    string? Description,
    string ChannelId,
    string ChannelName,
    int VideoCount,
    string? Thumbnail,
    List<PlaylistVideoInfo> Videos);

public record YouTubeChannelResponse(
    string ChannelId,
    string Title,
    string? Description,
    long? SubscriberCount,
    int VideoCount,
    string? Thumbnail,
    List<ChannelVideoInfo> Videos);

public record FetchUrlResponse(YouTubeContentType Type, string? VideoId, string? PlaylistId, string? ChannelId, string? Error);

public record YouTubeUrlFetchResult(
    YouTubeContentType Type,
    YouTubeVideoResponse? VideoInfo,
    YouTubePlaylistResponse? PlaylistInfo,
    YouTubeChannelResponse? ChannelInfo);

public record YouTubeFetchStats(long TotalCacheHits, long TotalCacheMisses, double CacheHitRate, long CachedItemsCount);

public record YouTubeSearchResponse(string Query, List<YouTubeSearchItem> Items, int TotalResults, string? NextPageToken);

public record YouTubeSearchItem(
    YouTubeContentType Type,
    string Id,
    string Title,
    string? Description,
    string? ChannelId,
    string? ChannelTitle,
    string? ThumbnailUrl,
    DateTime? PublishedAt,
    int? Duration,
    long? ViewCount);

public record BatchVideoRequest(List<string> VideoIds);

public class BatchVideoRequestValidator : AbstractValidator<BatchVideoRequest>
{
    public BatchVideoRequestValidator()
    {
        RuleFor(x => x.VideoIds).NotEmpty().WithMessage("视频ID列表不能为空").Must(ids => ids.Count <= 50).WithMessage("最多只能批量获取50个视频")
            .Must(ids => ids.All(id => !string.IsNullOrWhiteSpace(id))).WithMessage("视频ID不能为空");
    }
}

public record ValidateUrlsRequest(List<string> Urls);

public class ValidateUrlsRequestValidator : AbstractValidator<ValidateUrlsRequest>
{
    public ValidateUrlsRequestValidator()
    {
        RuleFor(x => x.Urls).NotEmpty().WithMessage("URL列表不能为空").Must(urls => urls.Count <= 100).WithMessage("最多只能验证100个URL");
    }
}

public record ValidateUrlsResponse(List<UrlValidationResult> Results);

public record UrlValidationResult(string Url, bool IsValid, string? Error);

public record VideoFormatsResponse(string VideoId, List<VideoStreamInfo> VideoFormats, List<AudioStreamInfo> AudioFormats, List<SubtitleInfo> Subtitles);