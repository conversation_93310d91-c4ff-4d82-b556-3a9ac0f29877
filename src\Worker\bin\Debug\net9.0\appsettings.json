{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"RabbitMQ": "amqp://guest:guest@localhost:5672/"}, "RabbitMQ": {"TaskQueueName": "task-queue"}, "CoreService": {"BaseUrl": "http://localhost:5126", "ApiKey": "worker-node-secret-key-2024"}, "Worker": {"BaseUrl": "http://localhost:5078", "NodeName": "Worker-Node-1", "YtDlpPath": "yt-dlp", "FfmpegPath": "ffmpeg", "DownloadPath": ""}, "RapidApi": {"ApiKey": "", "Host": "", "BaseUrl": "https://youtube-v31.p.rapidapi.com", "DailyLimit": 1000}}